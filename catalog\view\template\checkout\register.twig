<form id="form-register">
  <p>{{ text_login }}</p>
  <fieldset>
    <legend>{{ heading_title }}</legend>
    <div class="row">
      {% if config_checkout_guest %}
        <div class="col mb-3 required">
          <div class="form-check form-check-inline">
            <input type="radio" name="account" value="1" id="input-register" class="form-check-input"{% if account %} checked{% endif %}/> <label for="input-register" class="form-check-label">{{ text_register }}</label>
          </div>
          <div class="form-check form-check-inline">
            <input type="radio" name="account" value="0" id="input-guest" class="form-check-input"{% if not account %} checked{% endif %}/> <label for="input-guest" class="form-check-label">{{ text_guest }}</label>
          </div>
        </div>
      {% endif %}
      <div class="col mb-3{% if customer_groups|length <= 1 %} d-none{% endif %}">
        <label class="form-label">{{ entry_customer_group }}</label>
        <select name="customer_group_id" id="input-customer-group" class="form-select">
          {% for customer_group in customer_groups %}
            <option value="{{ customer_group.customer_group_id }}"{% if customer_group.customer_group_id == customer_group_id %} selected{% endif %}>{{ customer_group.name }}</option>
          {% endfor %}
        </select>
      </div>
    </div>
    <div class="row row-cols-1 row-cols-md-2">
      <div class="col mb-3 required">
        <label for="input-firstname" class="form-label">{{ entry_firstname }}</label>
        <input type="text" name="firstname" value="{{ firstname }}" placeholder="{{ entry_firstname }}" id="input-firstname" class="form-control"/>
        <div id="error-firstname" class="invalid-feedback"></div>
      </div>
      <div class="col mb-3 required">
        <label for="input-lastname" class="form-label">{{ entry_lastname }}</label>
        <input type="text" name="lastname" value="{{ lastname }}" placeholder="{{ entry_lastname }}" id="input-lastname" class="form-control"/>
        <div id="error-lastname" class="invalid-feedback"></div>
      </div>
      <div class="col mb-3 required">
        <label for="input-email" class="form-label">{{ entry_email }}</label>
        <input type="text" name="email" value="{{ email }}" placeholder="{{ entry_email }}" id="input-email" class="form-control"/>
        <div id="error-email" class="invalid-feedback"></div>
      </div>
      {% if config_telephone_display %}
        <div class="col mb-3{% if config_telephone_required %} required{% endif %}">
          <label for="input-telephone" class="form-label">{{ entry_telephone }}</label>
          <input type="text" name="telephone" value="{{ telephone }}" placeholder="{{ entry_telephone }}" id="input-telephone" class="form-control"/>
          <div id="error-telephone" class="invalid-feedback"></div>
        </div>
      {% endif %}
      {% for custom_field in custom_fields %}

        {% if custom_field.location == 'account' %}

          {% if custom_field.type == 'select' %}
            <div class="col mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label> <select name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" id="input-customer-custom-field-{{ custom_field.custom_field_id }}" class="form-select">
                <option value="">{{ text_select }}</option>
                {% for custom_field_value in custom_field.custom_field_value %}
                  <option value="{{ custom_field_value.custom_field_value_id }}"{% if account_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id == account_custom_field[custom_field.custom_field_id] %} selected{% endif %}>{{ custom_field_value.name }}</option>
                {% endfor %}
              </select>
              <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

          {% if custom_field.type == 'radio' %}
            <div class="col mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label class="form-label">{{ custom_field.name }}</label>
              <div id="input-custom-field-{{ custom_field.custom_field_id }}">
                {% for custom_field_value in custom_field.custom_field_value %}
                  <div class="form-check">
                    <input type="radio" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{{ custom_field_value.custom_field_value_id }}" id="input-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-input"{% if account_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id == account_custom_field[custom_field.custom_field_id] %} checked{% endif %}/>
                    <label for="input-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-label">{{ custom_field_value.name }}</label>
                  </div>
                {% endfor %}
              </div>
              <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

          {% if custom_field.type == 'checkbox' %}
            <div class="col mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label class="form-label">{{ custom_field.name }}</label>
              <div id="input-custom-field-{{ custom_field.custom_field_id }}">
                {% for custom_field_value in custom_field.custom_field_value %}
                  <div class="form-check">
                    <input type="checkbox" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}][]" value="{{ custom_field_value.custom_field_value_id }}" id="input-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-input"{% if account_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id in account_custom_field[custom_field.custom_field_id] %} checked{% endif %}/>
                    <label for="input-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-label">{{ custom_field_value.name }}</label>
                  </div>
                {% endfor %}
              </div>
              <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

          {% if custom_field.type == 'text' %}
            <div class="col mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
              <input type="text" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if account_custom_field[custom_field.custom_field_id] %}{{ account_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control"/>
              <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

          {% if custom_field.type == 'textarea' %}
            <div class="col mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label> <textarea name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" rows="5" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control">{% if account_custom_field[custom_field.custom_field_id] %}{{ account_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}</textarea>
              <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

          {% if custom_field.type == 'file' %}
            <div class="col mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
              <div>
                <button type="button" data-oc-toggle="upload" data-oc-url="{{ upload }}" data-oc-size-max="{{ config_file_max_size }}" data-oc-size-error="{{ error_upload_size }}" data-oc-target="#input-custom-field-{{ custom_field.custom_field_id }}" class="btn btn-light"><i class="fa-solid fa-upload"></i> {{ button_upload }}</button>
                <input type="hidden" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if account_custom_field[custom_field.custom_field_id] %}{{ account_custom_field[custom_field.custom_field_id] }}{% endif %}" id="input-custom-field-{{ custom_field.custom_field_id }}"/>
              </div>
              <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

          {% if custom_field.type == 'date' %}
            <div class="col mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
              <input type="date" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if account_custom_field[custom_field.custom_field_id] %}{{ account_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control"/>
              <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

          {% if custom_field.type == 'time' %}
            <div class="col mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
              <input type="time" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if account_custom_field[custom_field.custom_field_id] %}{{ account_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control"/>
              <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

          {% if custom_field.type == 'datetime' %}
            <div class="col mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
              <input type="datetime-local" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if account_custom_field[custom_field.custom_field_id] %}{{ account_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control"/>
              <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

        {% endif %}
      {% endfor %}
    </div>
  </fieldset>
  {% if config_checkout_payment_address %}
    <fieldset>
      <legend>{{ text_payment_address }}</legend>
      <div class="row row-cols-1 row-cols-md-2">
        <div class="col mb-3">
          <label for="input-payment-company" class="form-label">{{ entry_company }}</label>
          <input type="text" name="payment_company" value="{{ payment_company }}" placeholder="{{ entry_company }}" id="input-payment-company" class="form-control"/>
        </div>
        <div class="col mb-3 required">
          <label for="input-payment-address-1" class="form-label">{{ entry_address_1 }}</label>
          <input type="text" name="payment_address_1" value="{{ payment_address_1 }}" placeholder="{{ entry_address_1 }}" id="input-payment-address-1" class="form-control"/>
          <div id="error-payment-address-1" class="invalid-feedback"></div>
        </div>
        <div class="col mb-3">
          <label for="input-payment-address-2" class="form-label">{{ entry_address_2 }}</label>
          <input type="text" name="payment_address_2" value="{{ payment_address_2 }}" placeholder="{{ entry_address_2 }}" id="input-payment-address-2" class="form-control"/>
        </div>
        <div class="col mb-3 required">
          <label for="input-payment-city" class="form-label">{{ entry_city }}</label>
          <input type="text" name="payment_city" value="{{ payment_city }}" placeholder="{{ entry_city }}" id="input-payment-city" class="form-control"/>
          <div id="error-payment-city" class="invalid-feedback"></div>
        </div>
        <div class="col mb-3 required">
          <label for="input-payment-postcode" class="form-label">{{ entry_postcode }}</label>
          <input type="text" name="payment_postcode" value="{{ payment_postcode }}" placeholder="{{ entry_postcode }}" id="input-payment-postcode" class="form-control"/>
          <div id="error-payment-postcode" class="invalid-feedback"></div>
        </div>
        <div class="col mb-3 required">
          <label for="input-payment-country" class="form-label">{{ entry_country }}</label>
          <select name="payment_country_id" id="input-payment-country" class="form-select">
            <option value="">{{ text_select }}</option>
            {% for country in countries %}
              <option value="{{ country.country_id }}"{% if country.country_id == payment_country_id %} selected{% endif %}>{{ country.name }}</option>
            {% endfor %}
          </select>
          <div id="error-payment-country" class="invalid-feedback"></div>
        </div>
        <div class="col mb-3 required">
          <label for="input-payment-zone" class="form-label">{{ entry_zone }}</label>
          <select name="payment_zone_id" id="input-payment-zone" class="form-select" data-oc-value="{{ payment_zone_id }}">
            <option value="">{{ text_select }}</option>
            {% for zone in payment_zones %}
              <option value="{{ zone.zone_id }}"{% if zone.zone_id == payment_zone_id %} selected{% endif %}>{{ zone.name }}</option>
            {% endfor %}
          </select>
          <div id="error-payment-zone" class="invalid-feedback"></div>
        </div>
        {% for custom_field in custom_fields %}
          {% if custom_field.location == 'address' %}

            {% if custom_field.type == 'select' %}
              <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label for="input-payment-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label> <select name="payment_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" id="input-payment-custom-field-{{ custom_field.custom_field_id }}" class="form-select">
                  <option value="">{{ text_select }}</option>
                  {% for custom_field_value in custom_field.custom_field_value %}
                    <option value="{{ custom_field_value.custom_field_value_id }}"{% if payment_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id == payment_custom_field[custom_field.custom_field_id] %} selected{% endif %}>{{ custom_field_value.name }}</option>
                  {% endfor %}
                </select>
                <div id="error-payment-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
              </div>
            {% endif %}

            {% if custom_field.type == 'radio' %}
              <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label class="form-label">{{ custom_field.name }}</label>
                <div id="input-payment-custom-field-{{ custom_field.custom_field_id }}">
                  {% for custom_field_value in custom_field.custom_field_value %}
                    <div class="form-check">
                      <input type="radio" name="payment_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{{ custom_field_value.custom_field_value_id }}" id="input-payment-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-input"{% if payment_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id == payment_custom_field[custom_field.custom_field_id] %} checked{% endif %}/> <label for="input-payment-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-label">{{ custom_field_value.name }}</label>
                    </div>
                  {% endfor %}
                </div>
                <div id="error-payment-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
              </div>
            {% endif %}
            {% if custom_field.type == 'checkbox' %}
              <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label class="form-label">{{ custom_field.name }}</label>
                <div id="input-payment-custom-field-{{ custom_field.custom_field_id }}">
                  {% for custom_field_value in custom_field.custom_field_value %}
                    <div class="form-check">
                      <input type="checkbox" name="payment_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}][]" value="{{ custom_field_value.custom_field_value_id }}" id="input-payment-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-input"{% if address_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id in address_custom_field[custom_field.custom_field_id] %} checked{% endif %}/> <label for="input-payment-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-label">{{ custom_field_value.name }}</label>
                    </div>
                  {% endfor %}
                </div>
                <div id="error-payment-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
              </div>
            {% endif %}
            {% if custom_field.type == 'text' %}
              <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label for="input-payment-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
                <input type="text" name="payment_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if address_custom_field[custom_field.custom_field_id] %}{{ address_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-payment-custom-field-{{ custom_field.custom_field_id }}" class="form-control"/>
                <div id="error-payment-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
              </div>
            {% endif %}
            {% if custom_field.type == 'textarea' %}
              <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label for="input-payment-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label> <textarea name="payment_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" rows="5" placeholder="{{ custom_field.name }}" id="input-payment-custom-field-{{ custom_field.custom_field_id }}" class="form-control">{% if address_custom_field[custom_field.custom_field_id] %}{{ address_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}</textarea>
                <div id="error-payment-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
              </div>
            {% endif %}
            {% if custom_field.type == 'file' %}
              <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label class="form-label">{{ custom_field.name }}</label>
                <div>
                  <button type="button" data-oc-toggle="upload" data-oc-url="{{ upload }}" data-oc-size-max="{{ config_file_max_size }}" data-oc-size-error="{{ error_upload_size }}" data-oc-target="#input-payment-custom-field-{{ custom_field.custom_field_id }}" class="btn btn-light"><i class="fa-solid fa-upload"></i> {{ button_upload }}</button>
                  <input type="hidden" name="payment_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if payment_custom_field[custom_field.custom_field_id] %}{{ payment_custom_field[custom_field.custom_field_id] }}{% endif %}" id="input-payment-custom-field-{{ custom_field.custom_field_id }}"/>
                </div>
                <div id="error-payment-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
              </div>
            {% endif %}
            {% if custom_field.type == 'date' %}
              <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label for="input-payment-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
                <input type="date" name="payment_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if payment_custom_field[custom_field.custom_field_id] %}{{ payment_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-payment-custom-field-{{ custom_field.custom_field_id }}" class="form-control"/>
                <div id="error-payment-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
              </div>
            {% endif %}
            {% if custom_field.type == 'time' %}
              <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label for="input-payment-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
                <input type="time" name="payment_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if payment_custom_field[custom_field.custom_field_id] %}{{ payment_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-payment-custom-field-{{ custom_field.custom_field_id }}" class="form-control"/>
                <div id="error-payment-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
              </div>
            {% endif %}

            {% if custom_field.type == 'datetime' %}
              <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label for="input-payment-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
                <input type="datetime-local" name="payment_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if payment_custom_field[custom_field.custom_field_id] %}{{ payment_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-payment-custom-field-{{ custom_field.custom_field_id }}" class="form-control"/>
                <div id="error-payment-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
              </div>
            {% endif %}

          {% endif %}
        {% endfor %}

        {% if shipping_required %}
          <div class="col mb-3">
            <div class="form-check">
              <input type="checkbox" name="address_match" value="1" id="input-address-match" class="form-check-input" checked/> <label for="input-address-match" class="form-check-label">{{ entry_match }}</label>
            </div>
          </div>
        {% endif %}

      </div>
    </fieldset>
  {% endif %}
  <fieldset id="shipping-address" style="display: {% if not config_checkout_payment_address %}block{% else %}none{% endif %};">
    <legend>{{ text_shipping_address }}</legend>
    <div class="row row-cols-1 row-cols-md-2">
      {% if config_checkout_payment_address %}
        <div class="col mb-3 required">
          <label for="input-shipping-firstname" class="form-label">{{ entry_firstname }}</label>
          <input type="text" name="shipping_firstname" value="{{ shipping_firstname }}" placeholder="{{ entry_firstname }}" id="input-shipping-firstname" class="form-control"/>
          <div id="error-shipping-firstname" class="invalid-feedback"></div>
        </div>
        <div class="col mb-3 required">
          <label for="input-shipping-lastname" class="form-label">{{ entry_lastname }}</label>
          <input type="text" name="shipping_lastname" value="{{ shipping_lastname }}" placeholder="{{ entry_lastname }}" id="input-shipping-lastname" class="form-control"/>
          <div id="error-shipping-lastname" class="invalid-feedback"></div>
        </div>
      {% endif %}
      <div class="col mb-3">
        <label for="input-shipping-company" class="form-label">{{ entry_company }}</label>
        <input type="text" name="shipping_company" value="{{ shipping_company }}" placeholder="{{ entry_company }}" id="input-shipping-company" class="form-control"/>
      </div>
      <div class="col mb-3 required">
        <label for="input-shipping-address-1" class="form-label">{{ entry_address_1 }}</label>
        <input type="text" name="shipping_address_1" value="{{ shipping_address_1 }}" placeholder="{{ entry_address_1 }}" id="input-shipping-address-1" class="form-control"/>
        <div id="error-shipping-address-1" class="invalid-feedback"></div>
      </div>
      <div class="col mb-3">
        <label for="input-shipping-address-2" class="form-label">{{ entry_address_2 }}</label>
        <input type="text" name="shipping_address_2" value="{{ shipping_address_2 }}" placeholder="{{ entry_address_2 }}" id="input-shipping-address-2" class="form-control"/>
      </div>
      <div class="col mb-3 required">
        <label for="input-shipping-city" class="form-label">{{ entry_city }}</label>
        <input type="text" name="shipping_city" value="{{ shipping_city }}" placeholder="{{ entry_city }}" id="input-shipping-city" class="form-control"/>
        <div id="error-shipping-city" class="invalid-feedback"></div>
      </div>
      <div class="col mb-3 required">
        <label for="input-shipping-postcode" class="form-label">{{ entry_postcode }}</label>
        <input type="text" name="shipping_postcode" value="{{ shipping_postcode }}" placeholder="{{ entry_postcode }}" id="input-shipping-postcode" class="form-control"/>
        <div id="error-shipping-postcode" class="invalid-feedback"></div>
      </div>
      <div class="col mb-3 required">
        <label for="input-shipping-country" class="form-label">{{ entry_country }}</label>
        <select name="shipping_country_id" id="input-shipping-country" class="form-select">
          <option value="">{{ text_select }}</option>
          {% for country in countries %}
            <option value="{{ country.country_id }}"{% if country.country_id == shipping_country_id %} selected{% endif %}>{{ country.name }}</option>
          {% endfor %}
        </select>
        <div id="error-shipping-country" class="invalid-feedback"></div>
      </div>
      <div class="col mb-3 required">
        <label for="input-shipping-zone" class="form-label">{{ entry_zone }}</label>
        <select name="shipping_zone_id" id="input-shipping-zone" class="form-select" data-oc-value="{{ shipping_zone_id }}">
          <option value="">{{ text_select }}</option>
          {% for zone in shipping_zones %}
            <option value="{{ zone.zone_id }}"{% if zone.zone_id == shipping_zone_id %} selected{% endif %}>{{ zone.name }}</option>
          {% endfor %}
        </select>
        <div id="error-shipping-zone" class="invalid-feedback"></div>
      </div>
      {% for custom_field in custom_fields %}
        {% if custom_field.location == 'address' %}

          {% if custom_field.type == 'select' %}
            <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label for="input-shipping-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
              <select name="shipping_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" id="input-shipping-custom-field-{{ custom_field.custom_field_id }}" class="form-select">
                <option value="">{{ text_select }}</option>
                {% for custom_field_value in custom_field.custom_field_value %}
                  <option value="{{ custom_field_value.custom_field_value_id }}"{% if shipping_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id == shipping_custom_field[custom_field.custom_field_id] %} selected{% endif %}>{{ custom_field_value.name }}</option>
                {% endfor %}
              </select>
              <div id="error-shipping-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

          {% if custom_field.type == 'radio' %}
            <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label class="form-label">{{ custom_field.name }}</label>
              <div id="input-shipping-custom-field-{{ custom_field.custom_field_id }}">
                {% for custom_field_value in custom_field.custom_field_value %}
                  <div class="form-check">
                    <input type="radio" name="shipping_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{{ custom_field_value.custom_field_value_id }}" id="input-shipping-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-input"{% if shipping_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id in shipping_custom_field[custom_field.custom_field_id] %} checked{% endif %}/>
                    <label for="input-shipping-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-label">{{ custom_field_value.name }}</label>
                  </div>
                {% endfor %}
              </div>
              <div id="error-shipping-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

          {% if custom_field.type == 'checkbox' %}
            <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label class="form-label">{{ custom_field.name }}</label>
              <div id="input-shipping-custom-field-{{ custom_field.custom_field_id }}">
                {% for custom_field_value in custom_field.custom_field_value %}
                  <div class="form-check">
                    <input type="checkbox" name="shipping_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}][]" value="{{ custom_field_value.custom_field_value_id }}" id="input-shipping-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-input"{% if shipping_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id in shipping_custom_field[custom_field.custom_field_id] %} checked{% endif %}/>
                    <label for="input-shipping-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-label">{{ custom_field_value.name }}</label>
                  </div>
                {% endfor %}
              </div>
              <div id="error-shipping-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

          {% if custom_field.type == 'text' %}
            <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label for="input-shipping-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
              <input type="text" name="shipping_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if shipping_custom_field[custom_field.custom_field_id] %}{{ shipping_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-shipping-custom-field-{{ custom_field.custom_field_id }}" class="form-control"/>
              <div id="error-shipping-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

          {% if custom_field.type == 'textarea' %}
            <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label for="input-shipping-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
              <textarea name="shipping_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" rows="5" placeholder="{% if shipping_custom_field[custom_field.custom_field_id] %}{{ shipping_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" id="input-shipping-custom-field-{{ custom_field.custom_field_id }}" class="form-control">{{ custom_field.value }}</textarea>
              <div id="error-shipping-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}
          {% if custom_field.type == 'file' %}
            <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label class="form-label">{{ custom_field.name }}</label>
              <div>
                <button type="button" data-oc-toggle="upload" data-oc-url="{{ upload }}" data-oc-size-max="{{ config_file_max_size }}" data-oc-size-error="{{ error_upload_size }}" data-oc-target="#input-shipping-custom-field-{{ custom_field.custom_field_id }}" class="btn btn-light"><i class="fa-solid fa-upload"></i> {{ button_upload }}</button>
                <input type="hidden" name="shipping_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if shipping_custom_field[custom_field.custom_field_id] %}{{ shipping_custom_field[custom_field.custom_field_id] }}{% endif %}" id="input-shipping-custom-field-{{ custom_field.custom_field_id }}"/>
                <div id="error-shipping-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
              </div>
            </div>
          {% endif %}

          {% if custom_field.type == 'date' %}
            <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label for="input-shipping-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
              <input type="date" name="shipping_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if shipping_custom_field[custom_field.custom_field_id] %}{{ shipping_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-shipping-custom-field-{{ custom_field.custom_field_id }}" class="form-control"/>
              <div id="error-shipping-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

          {% if custom_field.type == 'time' %}
            <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label for="input-shipping-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
              <input type="time" name="shipping_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if shipping_custom_field[custom_field.custom_field_id] %}{{ shipping_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-shipping-custom-field-{{ custom_field.custom_field_id }}" class="form-control"/>
              <div id="error-shipping-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}

          {% if custom_field.type == 'datetime' %}
            <div class="col mb-3{% if custom_field.required %} required{% endif %} custom-field custom-field-{{ custom_field.custom_field_id }}">
              <label for="input-shipping-custom-field-{{ custom_field.custom_field_id }}" class="form-label">{{ custom_field.name }}</label>
              <input type="datetime-local" name="shipping_custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if shipping_custom_field[custom_field.custom_field_id] %}{{ shipping_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-shipping-custom-field-{{ custom_field.custom_field_id }}" class="form-control"/>
              <div id="error-shipping-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
            </div>
          {% endif %}
        {% endif %}
      {% endfor %}
    </div>
  </fieldset>
  <div id class="row row-cols-1 row-cols-md-2">
    <div id="password" class="col mb-3 required">
      <fieldset>
        <legend>{{ text_your_password }}</legend>
        <div class="row">
          <div class="col mb-3 required">
            <label for="input-password" class="form-label">{{ entry_password }}</label> <input type="password" name="password" value="" placeholder="{{ entry_password }}" id="input-password" class="form-control"/>
            <div id="error-password" class="invalid-feedback"></div>
          </div>
        </div>
      </fieldset>
    </div>
    <div class="col mb-3 required">{{ captcha }}</div>
  </div>
  <div id class="row">
    <div class="col">
      <div class="form-check form-switch form-switch-lg">
        <label for="input-newsletter" class="form-check-label">{{ entry_newsletter }}</label>
        <input type="checkbox" name="newsletter" value="1" id="input-newsletter" class="form-check-input"/>
      </div>
      {% if text_agree %}
        <div id="register-agree" class="form-check form-switch form-switch-lg">
          <label for="input-register-agree" class="form-check-label">{{ text_agree }}</label>
          <input type="checkbox" name="agree" value="1" id="input-register-agree" class="form-check-input"{% if agree %} checked{% endif %}/>
        </div>
      {% endif %}
      <button type="submit" id="button-register" class="btn btn-primary mt-2">{{ button_continue }}</button>
    </div>
  </div>
</form>
<script type="text/javascript"><!--
// Account
$('input[name=\'account\']').on('click', function() {
    if ($(this).val() == 1) {
        $('#password').removeClass('d-none');
    } else {
        // If guest hide password field
        $('#password').addClass('d-none');
    }

    if ($(this).val() == 1) {
        $('#register-agree').removeClass('d-none');
    } else {
        // If guest hide register agree field
        $('#register-agree').addClass('d-none');
    }
});

$('input[name=\'account\']:checked').trigger('click');

// Customer Group
$('#input-customer-group').on('change', function() {
    var element = this;

    chain.attach(function() {
        return $.ajax({
            url: 'index.php?route=account/custom_field&language={{ language }}&customer_group_id=' + $(element).val(),
            dataType: 'json',
            beforeSend: function() {
                $(element).prop('disabled', true);
            },
            complete: function() {
                $(element).prop('disabled', false);
            },
            success: function(json) {
                $('.custom-field').addClass('d-none');
                $('.custom-field').removeClass('required');

                for (i = 0; i < json.length; i++) {
                    custom_field = json[i];

                    $('.custom-field-' + custom_field['custom_field_id']).removeClass('d-none');

                    if (custom_field['required']) {
                        $('.custom-field-' + custom_field['custom_field_id']).addClass('required');
                    }
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            }
        });
    });
});

$('#input-customer-group').trigger('change');

$('#input-address-match').on('change', function() {
    if ($(this).prop('checked')) {
        $('#shipping-address').hide();
    } else {
        $('#shipping-address').show();
    }
});

$('#form-register').on('submit', function(e) {
    e.preventDefault();

    var element = this;

    chain.attach(function() {
        return $.ajax({
            url: 'index.php?route=checkout/register.save&language={{ language }}',
            type: 'post',
            dataType: 'json',
            data: $('#form-register').serialize(),
            contentType: 'application/x-www-form-urlencoded',
            beforeSend: function() {
                $('#button-register').button('loading');
            },
            complete: function() {
                $('#button-register').button('reset');
            },
            success: function(json) {
                console.log(json);

                $('#form-register').find('.is-invalid').removeClass('is-invalid');
                $('#form-register').find('.invalid-feedback').removeClass('d-block');

                if (json['redirect']) {
                    location = json['redirect'];
                }

                if (json['error']) {
                    if (json['error']['warning']) {
                        $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa-solid fa-circle-exclamation"></i> ' + json['error']['warning'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                    }

                    for (key in json['error']) {
                        $('#input-' + key.replaceAll('_', '-')).addClass('is-invalid').find('.form-control, .form-select, .form-check-input, .form-check-label').addClass('is-invalid');
                        $('#error-' + key.replaceAll('_', '-')).html(json['error'][key]).addClass('d-block');
                    }
                }

                if (json['success']) {
                    $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fa-solid fa-circle-check"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');

                    if ($('#input-register').prop('checked')) {
                        $('input[name=\'account\']').prop('disabled', true);
                        $('#input-customer-group').prop('disabled', true);
                        $('#input-password').prop('disabled', true);
                        $('#input-captcha').prop('disabled', true);
                        $('#input-register-agree').prop('disabled', true);
                    }

                    $('#input-shipping-method').val('');
                    $('#input-payment-method').val('');

                    // Trigger auto-selection for both shipping and payment methods after customer info is saved
                    setTimeout(function() {
                        // Auto-select shipping method first (if shipping is required)
                        if ($('#input-shipping-method').length) {
                            $.ajax({
                                url: 'index.php?route=checkout/shipping_method.quote&language={{ language }}',
                                dataType: 'json',
                                success: function(json) {
                                    if (json['auto_selected'] && json['selected_method']) {
                                        // Auto-selected single shipping method
                                        $('#input-shipping-method').val(json['selected_method']['name'] + ' - ' + json['selected_method']['text']);
                                        $('#input-shipping-code').val(json['selected_method']['code']);

                                        // Replace the choose button with a checkmark
                                        $('#button-shipping-methods').replaceWith('<span class="input-group-text bg-success text-white"><i class="fa fa-check"></i> Selected</span>');
                                        $('#input-shipping-method').prop('readonly', true);

                                        // After shipping is selected, auto-select payment method
                                        setTimeout(function() {
                                            autoSelectPaymentMethod();
                                        }, 500);
                                    } else {
                                        // No shipping auto-selection, try payment anyway
                                        setTimeout(function() {
                                            autoSelectPaymentMethod();
                                        }, 500);
                                    }
                                },
                                error: function() {
                                    // Fallback: trigger the button click
                                    $('#button-shipping-methods').trigger('click');
                                    // Still try payment method
                                    setTimeout(function() {
                                        autoSelectPaymentMethod();
                                    }, 1000);
                                }
                            });
                        } else {
                            // No shipping required, go straight to payment
                            autoSelectPaymentMethod();
                        }

                        // Function to auto-select payment method
                        function autoSelectPaymentMethod() {
                            $.ajax({
                                url: 'index.php?route=checkout/payment_method.getMethods&language={{ language }}',
                                dataType: 'json',
                                success: function(json) {
                                    if (json['error']) {
                                        return;
                                    }

                                    if (json['auto_selected'] && json['selected_method']) {
                                        // Auto-selected single payment method
                                        $('#input-payment-method').val(json['selected_method']['name']);
                                        $('#input-payment-code').val(json['selected_method']['code']);

                                        // Replace the choose button with a checkmark
                                        $('#button-payment-methods').replaceWith('<span class="input-group-text bg-success text-white"><i class="fa fa-check"></i> Selected</span>');
                                        $('#input-payment-method').prop('readonly', true);

                                        // Reload confirm section
                                        $('#checkout-confirm').load('index.php?route=checkout/confirm.confirm&language={{ language }}');
                                    }
                                },
                                error: function(xhr, ajaxOptions, thrownError) {
                                    // Fallback: trigger the button click
                                    $('#button-payment-methods').trigger('click');
                                }
                            });
                        }
                    }, 1500);

                    $('#checkout-confirm').load('index.php?route=checkout/confirm.confirm&language={{ language }}');
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            }
        });
    });
});

$('select[name$=\'_country_id\']').on('change', function() {
    var element = this;
    var type = $(this).attr('name').slice(0, -11);

    chain.attach(function() {
        return $.ajax({
            url: 'index.php?route=localisation/country&language={{ language }}&country_id=' + $('#input-' + type + '-country').val(),
            dataType: 'json',
            beforeSend: function() {
                $(element).prop('disabled', true);
                $('#input-' + type + '-zone').prop('disabled', true);
            },
            complete: function() {
                $(element).prop('disabled', false);
                $('#input-' + type + '-zone').prop('disabled', false);
            },
            success: function(json) {
                if (json['postcode_required'] == '1') {
                    $('#input-' + type + '-postcode').parent().addClass('required');
                } else {
                    $('#input-' + type + '-postcode').parent().removeClass('required');
                }

                html = '<option value="">{{ text_select|escape('js') }}</option>';

                if (json['zone'] && json['zone'] != '') {
                    for (i = 0; i < json['zone'].length; i++) {
                        html += '<option value="' + json['zone'][i]['zone_id'] + '"';

                        if (json['zone'][i]['zone_id'] == $('#input-' + type + '-zone').attr('data-oc-value')) {
                            html += ' selected';
                        }

                        html += '>' + json['zone'][i]['name'] + '</option>';
                    }
                } else {
                    html += '<option value="0" selected>{{ text_none|escape('js') }}</option>';
                }

                $('#input-' + type + '-zone').html(html);
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            }
        });
    });
});
//--></script>
