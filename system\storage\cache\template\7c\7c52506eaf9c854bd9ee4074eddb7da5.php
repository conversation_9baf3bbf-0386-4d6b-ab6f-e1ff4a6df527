<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* catalog/view/template/checkout/payment_method.twig */
class __TwigTemplate_26641855be7a85d88ed40e4cc2aa1617 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 1
        yield "<fieldset>
  <legend>";
        // line 2
        yield ($context["heading_title"] ?? null);
        yield "</legend>
  <div class=\"input-group\">
    <span class=\"input-group-text\"><i class=\"fa fa-credit-card\"></i></span><input type=\"text\" name=\"payment_method\" value=\"";
        // line 4
        yield ($context["payment_method"] ?? null);
        yield "\" placeholder=\"";
        yield ($context["entry_payment_method"] ?? null);
        yield "\" id=\"input-payment-method\" class=\"form-control\" readonly/>
    <button type=\"button\" id=\"button-payment-methods\" class=\"btn btn-primary\">";
        // line 5
        yield ($context["button_choose"] ?? null);
        yield "</button>
  </div>
  <input type=\"hidden\" name=\"code\" value=\"";
        // line 7
        yield ($context["code"] ?? null);
        yield "\" id=\"input-payment-code\"/>
  <div id=\"error-payment-method\" class=\"invalid-feedback\"></div>

  <!-- Inline payment methods container -->
  <div id=\"payment-methods-container\" class=\"mt-3\" style=\"display: none;\">
    <div class=\"card\">
      <div class=\"card-header\">
        <h6 class=\"mb-0\"><i class=\"fa fa-credit-card\"></i> ";
        // line 14
        yield ($context["text_payment_method"] ?? null);
        yield "</h6>
      </div>
      <div class=\"card-body\">
        <form id=\"form-payment-method\">
          <p>";
        // line 18
        yield ($context["text_payment"] ?? null);
        yield "</p>
          <div id=\"payment-methods-list\"></div>
          <div class=\"text-end mt-3\">
            <button type=\"submit\" id=\"button-payment-method\" class=\"btn btn-primary\">";
        // line 21
        yield ($context["button_continue"] ?? null);
        yield "</button>
          </div>
          <p>After Choosing Method, Click <strong class=\"red\">Continue</strong> and then <strong class=\"red\">Confirm Order button below</strong> to complete your order.</p>
        </form>
      </div>
    </div>
  </div>
</fieldset>
<br/>
<div class=\"mb-2\">
  <label for=\"input-comment\" class=\"form-label\"><strong>";
        // line 31
        yield ($context["text_comments"] ?? null);
        yield "</strong></label>
  <textarea name=\"comment\" rows=\"4\" id=\"input-comment\" class=\"form-control\">";
        // line 32
        yield ($context["comment"] ?? null);
        yield "</textarea>
</div>
";
        // line 34
        if (($context["text_agree"] ?? null)) {
            // line 35
            yield "  <div class=\"form-check form-switch form-switch-lg form-check-reverse mt-3\">
    <label for=\"input-checkout-agree\" class=\"form-check-label\">";
            // line 36
            yield ($context["text_agree"] ?? null);
            yield "</label> <input type=\"checkbox\" name=\"agree\" value=\"1\" id=\"input-checkout-agree\" class=\"form-check-input\"";
            if (($context["agree"] ?? null)) {
                yield " checked";
            }
            yield "/>
  </div>
";
        }
        // line 39
        yield "<script type=\"text/javascript\"><!--
\$('#button-payment-methods').on('click', function() {
    var element = this;

    chain.attach(function() {
        return \$.ajax({
            url: 'index.php?route=checkout/payment_method.getMethods&language=";
        // line 45
        yield ($context["language"] ?? null);
        yield "',
            dataType: 'json',
            beforeSend: function() {
                \$(element).button('loading');
            },
            complete: function() {
                \$(element).button('reset');
            },
            success: function(json) {
                \$('#input-payment-method').removeClass('is-invalid');
                \$('#error-payment-method').removeClass('d-block');

                if (json['error']) {
                    \$('#input-payment-method').addClass('is-invalid');
                    \$('#error-payment-method').html(json['error']).addClass('d-block');
                }

                if (json['payment_methods']) {
                    // Check if payment method was auto-selected
                    if (json['auto_selected'] && json['selected_method']) {
                        // Auto-selected single payment method
                        \$('#input-payment-method').val(json['selected_method']['name']);
                        \$('#input-payment-code').val(json['selected_method']['code']);

                        // Replace the choose button with a checkmark to show it's selected
                        \$('#button-payment-methods').replaceWith('<span class=\"input-group-text bg-success text-white\"><i class=\"fa fa-check\"></i> Selected</span>');
                        \$('#input-payment-method').prop('readonly', true);

                        // Load the confirm section since payment method is already selected
                        \$('#checkout-confirm').load('index.php?route=checkout/confirm.confirm&language=";
        // line 74
        yield ($context["language"] ?? null);
        yield "');
                    } else {
                        // Multiple payment methods - show selection interface
                        // Clear existing payment methods
                        \$('#payment-methods-list').empty();

                        var html = '';
                        var first = true;

                        for (i in json['payment_methods']) {
                            html += '<p><strong>' + json['payment_methods'][i]['name'] + '</strong></p>';

                            if (!json['payment_methods'][i]['error']) {
                                for (j in json['payment_methods'][i]['option']) {
                                    html += '<div class=\"form-check\">';

                                    var code = i + '-' + j.replaceAll('_', '-');

                                    html += '<input type=\"radio\" name=\"payment_method\" value=\"' + json['payment_methods'][i]['option'][j]['code'] + '\" id=\"input-payment-method-' + code + '\"';

                                    var method = \$('#input-payment-code').val();

                                    if ((json['payment_methods'][i]['option'][j]['code'] == method) || (!method && first)) {
                                        html += ' checked';

                                        first = false;
                                    }

                                    html += '/>';
                                    html += '  <label for=\"input-payment-method-' + code + '\" class=\"form-check-label\">' + json['payment_methods'][i]['option'][j]['name'] + '</label>';
                                    html += '</div>';
                                }
                            } else {
                                html += '<div class=\"alert alert-danger\">' + json['payment_methods'][i]['error'] + '</div>';
                            }
                        }

                        // Populate the inline container and show it
                        \$('#payment-methods-list').html(html);
                        \$('#payment-methods-container').show();
                    }
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
            }
        });
    });
});

\$(document).on('submit', '#form-payment-method', function(e) {
    e.preventDefault();

    var element = this;

    chain.attach(function() {
        return \$.ajax({
            url: 'index.php?route=checkout/payment_method.save&language=";
        // line 131
        yield ($context["language"] ?? null);
        yield "',
            type: 'post',
            data: \$('#form-payment-method').serialize(),
            dataType: 'json',
            contentType: 'application/x-www-form-urlencoded',
            beforeSend: function() {
                \$('#button-payment-method').button('loading');
            },
            complete: function() {
                \$('#button-payment-method').button('reset');
            },
            success: function(json) {
                console.log(json);

                if (json['redirect']) {
                    location = json['redirect'];
                }

                if (json['error']) {
                    \$('#alert').prepend('<div class=\"alert alert-danger alert-dismissible\"><i class=\"fa-solid fa-circle-exclamation\"></i> ' + json['error'] + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');
                }

                if (json['success']) {
                    \$('#alert').prepend('<div class=\"alert alert-success alert-dismissible\"><i class=\"fa-solid fa-circle-check\"></i> ' + json['success'] + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');

                    // Keep the payment methods container open - don't hide it
                    // \$('#payment-methods-container').hide();

                    \$('#input-payment-method').val(\$('input[name=\\'payment_method\\']:checked').parent().find('label').text());
                    \$('#input-payment-code').val(\$('input[name=\\'payment_method\\']:checked').val());

                    \$('#checkout-confirm').load('index.php?route=checkout/confirm.confirm&language=";
        // line 162
        yield ($context["language"] ?? null);
        yield "');
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
            }
        });
    });
});

// Comment
var timer = '';

\$('#input-comment').on('keydown', function() {
    \$('#button-confirm').prop('disabled', true);

    // Request
    clearTimeout(timer);

    timer = setTimeout(function(object) {
        chain.attach(function() {
            return \$.ajax({
                url: 'index.php?route=checkout/payment_method.comment&language=";
        // line 184
        yield ($context["language"] ?? null);
        yield "',
                type: 'post',
                data: \$('#input-comment').serialize(),
                dataType: 'json',
                contentType: 'application/x-www-form-urlencoded',
                success: function(json) {
                    console.log(json);

                    \$('.alert-dismissible').remove();

                    if (json['redirect']) {
                        location = json['redirect'];
                    }

                    if (json['error']) {
                        \$('#alert').prepend('<div class=\"alert alert-danger alert-dismissible\"><i class=\"fa-solid fa-circle-exclamation\"></i> ' + json['error'] + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');

                        \$('#button-confirm').prop('disabled', false);
                    }

                    if (json['success']) {
                        \$('#alert').prepend('<div class=\"alert alert-success alert-dismissible\"><i class=\"fa-solid fa-circle-check\"></i> ' + json['success'] + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');

                        \$('#button-confirm').prop('disabled', false);
                    }

                    window.setTimeout(function() {
                        \$('.alert-dismissible').fadeTo(1000, 0, function() {
                            \$(this).remove();
                        });
                    }, 3000);
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    \$('#button-confirm').prop('disabled', false);

                    console.log(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
                }
            });
        });
    }, 1000, this);
});

/* Agree to terms */
\$('#input-checkout-agree').on('change', function() {
    var element = this;

    chain.attach(function() {
        return \$.ajax({
            url: 'index.php?route=checkout/payment_method.agree&language=";
        // line 232
        yield ($context["language"] ?? null);
        yield "',
            type: 'post',
            data: \$('#input-checkout-agree').serialize(),
            dataType: 'json',
            contentType: 'application/x-www-form-urlencoded',
            beforeSend: function() {
                \$('#button-confirm').button('loading');
            },
            complete: function() {
                \$('#button-confirm').button('reset');
            },
            success: function(json) {
                \$('#checkout-confirm').load('index.php?route=checkout/confirm.confirm&language=";
        // line 244
        yield ($context["language"] ?? null);
        yield "');
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
            }
        });
    });
});

// Check if payment method is already selected on page load and restore visual state
\$(document).ready(function() {
    if (\$('#input-payment-method').val() && \$('#input-payment-code').val()) {
        // Payment method already selected, style it appropriately
        \$('#button-payment-methods').replaceWith('<span class=\"input-group-text bg-success text-white\"><i class=\"fa fa-check\"></i> Selected</span>');
    }
});
//--></script>
";
        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "catalog/view/template/checkout/payment_method.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  346 => 244,  331 => 232,  280 => 184,  255 => 162,  221 => 131,  161 => 74,  129 => 45,  121 => 39,  111 => 36,  108 => 35,  106 => 34,  101 => 32,  97 => 31,  84 => 21,  78 => 18,  71 => 14,  61 => 7,  56 => 5,  50 => 4,  45 => 2,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<fieldset>
  <legend>{{ heading_title }}</legend>
  <div class=\"input-group\">
    <span class=\"input-group-text\"><i class=\"fa fa-credit-card\"></i></span><input type=\"text\" name=\"payment_method\" value=\"{{ payment_method }}\" placeholder=\"{{ entry_payment_method }}\" id=\"input-payment-method\" class=\"form-control\" readonly/>
    <button type=\"button\" id=\"button-payment-methods\" class=\"btn btn-primary\">{{ button_choose }}</button>
  </div>
  <input type=\"hidden\" name=\"code\" value=\"{{ code }}\" id=\"input-payment-code\"/>
  <div id=\"error-payment-method\" class=\"invalid-feedback\"></div>

  <!-- Inline payment methods container -->
  <div id=\"payment-methods-container\" class=\"mt-3\" style=\"display: none;\">
    <div class=\"card\">
      <div class=\"card-header\">
        <h6 class=\"mb-0\"><i class=\"fa fa-credit-card\"></i> {{ text_payment_method }}</h6>
      </div>
      <div class=\"card-body\">
        <form id=\"form-payment-method\">
          <p>{{ text_payment }}</p>
          <div id=\"payment-methods-list\"></div>
          <div class=\"text-end mt-3\">
            <button type=\"submit\" id=\"button-payment-method\" class=\"btn btn-primary\">{{ button_continue }}</button>
          </div>
          <p>After Choosing Method, Click <strong class=\"red\">Continue</strong> and then <strong class=\"red\">Confirm Order button below</strong> to complete your order.</p>
        </form>
      </div>
    </div>
  </div>
</fieldset>
<br/>
<div class=\"mb-2\">
  <label for=\"input-comment\" class=\"form-label\"><strong>{{ text_comments }}</strong></label>
  <textarea name=\"comment\" rows=\"4\" id=\"input-comment\" class=\"form-control\">{{ comment }}</textarea>
</div>
{% if text_agree %}
  <div class=\"form-check form-switch form-switch-lg form-check-reverse mt-3\">
    <label for=\"input-checkout-agree\" class=\"form-check-label\">{{ text_agree }}</label> <input type=\"checkbox\" name=\"agree\" value=\"1\" id=\"input-checkout-agree\" class=\"form-check-input\"{% if agree %} checked{% endif %}/>
  </div>
{% endif %}
<script type=\"text/javascript\"><!--
\$('#button-payment-methods').on('click', function() {
    var element = this;

    chain.attach(function() {
        return \$.ajax({
            url: 'index.php?route=checkout/payment_method.getMethods&language={{ language }}',
            dataType: 'json',
            beforeSend: function() {
                \$(element).button('loading');
            },
            complete: function() {
                \$(element).button('reset');
            },
            success: function(json) {
                \$('#input-payment-method').removeClass('is-invalid');
                \$('#error-payment-method').removeClass('d-block');

                if (json['error']) {
                    \$('#input-payment-method').addClass('is-invalid');
                    \$('#error-payment-method').html(json['error']).addClass('d-block');
                }

                if (json['payment_methods']) {
                    // Check if payment method was auto-selected
                    if (json['auto_selected'] && json['selected_method']) {
                        // Auto-selected single payment method
                        \$('#input-payment-method').val(json['selected_method']['name']);
                        \$('#input-payment-code').val(json['selected_method']['code']);

                        // Replace the choose button with a checkmark to show it's selected
                        \$('#button-payment-methods').replaceWith('<span class=\"input-group-text bg-success text-white\"><i class=\"fa fa-check\"></i> Selected</span>');
                        \$('#input-payment-method').prop('readonly', true);

                        // Load the confirm section since payment method is already selected
                        \$('#checkout-confirm').load('index.php?route=checkout/confirm.confirm&language={{ language }}');
                    } else {
                        // Multiple payment methods - show selection interface
                        // Clear existing payment methods
                        \$('#payment-methods-list').empty();

                        var html = '';
                        var first = true;

                        for (i in json['payment_methods']) {
                            html += '<p><strong>' + json['payment_methods'][i]['name'] + '</strong></p>';

                            if (!json['payment_methods'][i]['error']) {
                                for (j in json['payment_methods'][i]['option']) {
                                    html += '<div class=\"form-check\">';

                                    var code = i + '-' + j.replaceAll('_', '-');

                                    html += '<input type=\"radio\" name=\"payment_method\" value=\"' + json['payment_methods'][i]['option'][j]['code'] + '\" id=\"input-payment-method-' + code + '\"';

                                    var method = \$('#input-payment-code').val();

                                    if ((json['payment_methods'][i]['option'][j]['code'] == method) || (!method && first)) {
                                        html += ' checked';

                                        first = false;
                                    }

                                    html += '/>';
                                    html += '  <label for=\"input-payment-method-' + code + '\" class=\"form-check-label\">' + json['payment_methods'][i]['option'][j]['name'] + '</label>';
                                    html += '</div>';
                                }
                            } else {
                                html += '<div class=\"alert alert-danger\">' + json['payment_methods'][i]['error'] + '</div>';
                            }
                        }

                        // Populate the inline container and show it
                        \$('#payment-methods-list').html(html);
                        \$('#payment-methods-container').show();
                    }
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
            }
        });
    });
});

\$(document).on('submit', '#form-payment-method', function(e) {
    e.preventDefault();

    var element = this;

    chain.attach(function() {
        return \$.ajax({
            url: 'index.php?route=checkout/payment_method.save&language={{ language }}',
            type: 'post',
            data: \$('#form-payment-method').serialize(),
            dataType: 'json',
            contentType: 'application/x-www-form-urlencoded',
            beforeSend: function() {
                \$('#button-payment-method').button('loading');
            },
            complete: function() {
                \$('#button-payment-method').button('reset');
            },
            success: function(json) {
                console.log(json);

                if (json['redirect']) {
                    location = json['redirect'];
                }

                if (json['error']) {
                    \$('#alert').prepend('<div class=\"alert alert-danger alert-dismissible\"><i class=\"fa-solid fa-circle-exclamation\"></i> ' + json['error'] + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');
                }

                if (json['success']) {
                    \$('#alert').prepend('<div class=\"alert alert-success alert-dismissible\"><i class=\"fa-solid fa-circle-check\"></i> ' + json['success'] + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');

                    // Keep the payment methods container open - don't hide it
                    // \$('#payment-methods-container').hide();

                    \$('#input-payment-method').val(\$('input[name=\\'payment_method\\']:checked').parent().find('label').text());
                    \$('#input-payment-code').val(\$('input[name=\\'payment_method\\']:checked').val());

                    \$('#checkout-confirm').load('index.php?route=checkout/confirm.confirm&language={{ language }}');
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
            }
        });
    });
});

// Comment
var timer = '';

\$('#input-comment').on('keydown', function() {
    \$('#button-confirm').prop('disabled', true);

    // Request
    clearTimeout(timer);

    timer = setTimeout(function(object) {
        chain.attach(function() {
            return \$.ajax({
                url: 'index.php?route=checkout/payment_method.comment&language={{ language }}',
                type: 'post',
                data: \$('#input-comment').serialize(),
                dataType: 'json',
                contentType: 'application/x-www-form-urlencoded',
                success: function(json) {
                    console.log(json);

                    \$('.alert-dismissible').remove();

                    if (json['redirect']) {
                        location = json['redirect'];
                    }

                    if (json['error']) {
                        \$('#alert').prepend('<div class=\"alert alert-danger alert-dismissible\"><i class=\"fa-solid fa-circle-exclamation\"></i> ' + json['error'] + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');

                        \$('#button-confirm').prop('disabled', false);
                    }

                    if (json['success']) {
                        \$('#alert').prepend('<div class=\"alert alert-success alert-dismissible\"><i class=\"fa-solid fa-circle-check\"></i> ' + json['success'] + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');

                        \$('#button-confirm').prop('disabled', false);
                    }

                    window.setTimeout(function() {
                        \$('.alert-dismissible').fadeTo(1000, 0, function() {
                            \$(this).remove();
                        });
                    }, 3000);
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    \$('#button-confirm').prop('disabled', false);

                    console.log(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
                }
            });
        });
    }, 1000, this);
});

/* Agree to terms */
\$('#input-checkout-agree').on('change', function() {
    var element = this;

    chain.attach(function() {
        return \$.ajax({
            url: 'index.php?route=checkout/payment_method.agree&language={{ language }}',
            type: 'post',
            data: \$('#input-checkout-agree').serialize(),
            dataType: 'json',
            contentType: 'application/x-www-form-urlencoded',
            beforeSend: function() {
                \$('#button-confirm').button('loading');
            },
            complete: function() {
                \$('#button-confirm').button('reset');
            },
            success: function(json) {
                \$('#checkout-confirm').load('index.php?route=checkout/confirm.confirm&language={{ language }}');
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
            }
        });
    });
});

// Check if payment method is already selected on page load and restore visual state
\$(document).ready(function() {
    if (\$('#input-payment-method').val() && \$('#input-payment-code').val()) {
        // Payment method already selected, style it appropriately
        \$('#button-payment-methods').replaceWith('<span class=\"input-group-text bg-success text-white\"><i class=\"fa fa-check\"></i> Selected</span>');
    }
});
//--></script>
", "catalog/view/template/checkout/payment_method.twig", "D:\\wamp64\\www\\pex\\pondexpo\\catalog\\view\\template\\checkout\\payment_method.twig");
    }
}
