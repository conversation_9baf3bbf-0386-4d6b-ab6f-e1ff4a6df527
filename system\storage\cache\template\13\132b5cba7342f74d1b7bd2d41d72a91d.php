<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* extension/opencart/catalog/view/template/module/blog.twig */
class __TwigTemplate_56e3c74c1211ebfef580657a3722a52f extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 1
        yield "<h2 class=\"lined-heading-wmt\">";
        yield ($context["heading_title"] ?? null);
        yield "</h2>
<div class=\"row row-cols-1 row-cols-sm-2 row-cols-md-2 row-cols-xl-3\">
  ";
        // line 3
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(($context["blogs"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["blog"]) {
            // line 4
            yield "    <div class=\"col mb-4\">
      <div class=\"blog-thumb slide-fadein\">
        <div class=\"image\">
          <a href=\"";
            // line 7
            yield CoreExtension::getAttribute($this->env, $this->source, $context["blog"], "href", [], "any", false, false, false, 7);
            yield "\"><img src=\"";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["blog"], "thumb", [], "any", false, false, false, 7);
            yield "\" alt=\"";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["blog"], "name", [], "any", false, false, false, 7);
            yield "\" title=\"";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["blog"], "name", [], "any", false, false, false, 7);
            yield "\" class=\"img-fluid\"/></a>
        </div>
        <div class=\"content\">
          <div class=\"description\">
            <h4><a href=\"";
            // line 11
            yield CoreExtension::getAttribute($this->env, $this->source, $context["blog"], "href", [], "any", false, false, false, 11);
            yield "\">";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["blog"], "name", [], "any", false, false, false, 11);
            yield "</a></h4>
            <p>";
            // line 12
            yield CoreExtension::getAttribute($this->env, $this->source, $context["blog"], "description", [], "any", false, false, false, 12);
            yield " <a href=\"";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["blog"], "href", [], "any", false, false, false, 12);
            yield "\">Read more</a></p>
          </div>
        </div>
      </div>
    </div>
  ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['blog'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 18
        yield "</div>
";
        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "extension/opencart/catalog/view/template/module/blog.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  90 => 18,  76 => 12,  70 => 11,  57 => 7,  52 => 4,  48 => 3,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<h2 class=\"lined-heading-wmt\">{{ heading_title }}</h2>
<div class=\"row row-cols-1 row-cols-sm-2 row-cols-md-2 row-cols-xl-3\">
  {% for blog in blogs %}
    <div class=\"col mb-4\">
      <div class=\"blog-thumb slide-fadein\">
        <div class=\"image\">
          <a href=\"{{ blog.href }}\"><img src=\"{{ blog.thumb }}\" alt=\"{{ blog.name }}\" title=\"{{ blog.name }}\" class=\"img-fluid\"/></a>
        </div>
        <div class=\"content\">
          <div class=\"description\">
            <h4><a href=\"{{ blog.href }}\">{{ blog.name }}</a></h4>
            <p>{{ blog.description }} <a href=\"{{ blog.href }}\">Read more</a></p>
          </div>
        </div>
      </div>
    </div>
  {% endfor %}
</div>
", "extension/opencart/catalog/view/template/module/blog.twig", "D:\\wamp64\\www\\pex\\pondexpo\\extension\\opencart\\catalog\\view\\template\\module\\blog.twig");
    }
}
