<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* catalog/view/template/mail/order_history.twig */
class __TwigTemplate_c3847c7f34523221addabd47064426d1 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 1
        yield ($context["text_order_id"] ?? null);
        yield " ";
        yield ($context["order_id"] ?? null);
        yield "<br/>
";
        // line 2
        yield ($context["text_date_added"] ?? null);
        yield " ";
        yield ($context["date_added"] ?? null);
        yield "<br/>
<br/>
";
        // line 4
        yield ($context["text_order_status"] ?? null);
        yield "<br/>
<br/>
";
        // line 6
        yield ($context["order_status"] ?? null);
        yield "<br/>
<br/>
";
        // line 8
        if (($context["link"] ?? null)) {
            // line 9
            yield ($context["text_link"] ?? null);
            yield "<br/>
<br/>
";
            // line 11
            yield ($context["link"] ?? null);
            yield "<br/>
";
        }
        // line 13
        if (($context["comment"] ?? null)) {
            // line 14
            yield "<br/>
";
            // line 15
            yield ($context["text_comment"] ?? null);
            yield "<br/>
<br/>
";
            // line 17
            yield ($context["comment"] ?? null);
            yield "<br/>
";
            // line 18
            yield ($context["text_footer"] ?? null);
            yield "<br/>
";
        } else {
            // line 19
            yield " 
";
            // line 20
            yield ($context["text_footer"] ?? null);
            yield "<br/>
";
        }
        // line 22
        yield "<br/>
";
        // line 23
        yield ($context["store"] ?? null);
        yield "<br/>
";
        // line 24
        yield ($context["store_url"] ?? null);
        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "catalog/view/template/mail/order_history.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  111 => 24,  107 => 23,  104 => 22,  99 => 20,  96 => 19,  91 => 18,  87 => 17,  82 => 15,  79 => 14,  77 => 13,  72 => 11,  67 => 9,  65 => 8,  60 => 6,  55 => 4,  48 => 2,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{{ text_order_id }} {{ order_id }}<br/>
{{ text_date_added }} {{ date_added }}<br/>
<br/>
{{ text_order_status }}<br/>
<br/>
{{ order_status }}<br/>
<br/>
{% if link %}
{{ text_link }}<br/>
<br/>
{{ link }}<br/>
{% endif %}
{% if comment %}
<br/>
{{ text_comment }}<br/>
<br/>
{{ comment }}<br/>
{{ text_footer }}<br/>
{% else %} 
{{ text_footer }}<br/>
{% endif %}
<br/>
{{ store }}<br/>
{{ store_url }}", "catalog/view/template/mail/order_history.twig", "D:\\wamp64\\www\\pex\\pondexpo\\catalog\\view\\template\\mail\\order_history.twig");
    }
}
