<?php
namespace Opencart\Catalog\Controller\Information;

class MakeOffer extends \Opencart\System\Engine\Controller {
	public function index(): void {
		$route = $this->request->get['route'] ?? '';
		$information_id = $this->request->get['information_id'] ?? 0;

		// Show makeoffer form for either route
		if ($route === 'information/makeoffer' || ($route === 'information/information' && $information_id == 5)) {
			$this->load->language('information/makeoffer');

			$this->document->setTitle($this->language->get('heading_title'));

			$data['breadcrumbs'] = [];

			$data['breadcrumbs'][] = [
				'text' => $this->language->get('text_home'),
				'href' => $this->url->link('common/home', 'language=' . $this->config->get('config_language'))
			];

			$data['breadcrumbs'][] = [
				'text' => $this->language->get('heading_title'),
				'href' => $this->url->link('information/makeoffer', 'language=' . $this->config->get('config_language'))
			];

			$data['send'] = $this->url->link('information/makeoffer.send', 'language=' . $this->config->get('config_language'));

			$data['name'] = $this->customer->getFirstName();
			$data['email'] = $this->customer->getEmail();
			$data['text_description'] = $this->language->get('text_description');

			// Form fields
			$data['form_title'] = $this->language->get('form_title');
			$data['entry_itemdescription'] = $this->language->get('entry_itemdescription');
			$data['entry_currentprice'] = $this->language->get('entry_currentprice');
			$data['entry_offerprice'] = $this->language->get('entry_offerprice');
			$data['entry_ziptoship'] = $this->language->get('entry_ziptoship');
			$data['entry_name'] = $this->language->get('entry_name');
			$data['entry_email'] = $this->language->get('entry_email');
			$data['entry_message'] = $this->language->get('entry_message');
			$data['button_submit'] = $this->language->get('button_submit');

			// Set default values
			$data['currentprice'] = '';

			// Captcha
			$this->load->model('setting/extension');

			$extension_info = $this->model_setting_extension->getExtensionByCode('captcha', $this->config->get('config_captcha'));

			if ($extension_info && $this->config->get('captcha_' . $this->config->get('config_captcha') . '_status') && in_array('makeoffer', (array)$this->config->get('config_captcha_page'))) {
				$data['captcha'] = $this->load->controller('extension/' . $extension_info['extension'] . '/captcha/' . $extension_info['code']);
			} else {
				$data['captcha'] = '';
			}

			$data['column_left'] = $this->load->controller('common/column_left');
			$data['column_right'] = $this->load->controller('common/column_right');
			$data['content_top'] = $this->load->controller('common/content_top');
			$data['content_bottom'] = $this->load->controller('common/content_bottom');
			$data['footer'] = $this->load->controller('common/footer');
			$data['header'] = $this->load->controller('common/header');

			$this->response->setOutput($this->load->view('information/makeoffer', $data));
		} else {
			// Default information page behavior
			$this->response->setOutput($this->load->controller('information/information'));
		}
	}

	public function send(): void {
		$this->load->language('information/makeoffer');

		$json = [];

		$required = [
			'itemdescription'    => '',
			'currentprice'   => '',
			'offerprice'   => '',
			'ziptoship'   => '',
			'name'    => '',
			'email'   => '',
			'message' => ''
		];

		$post_info = $this->request->post + $required;

		if (!oc_validate_length($post_info['itemdescription'], 3, 255)) {
			$json['error']['itemdescription'] = $this->language->get('error_product');
		}

		if (!is_numeric($post_info['currentprice']) || $post_info['currentprice'] <= 0) {
			$json['error']['currentprice'] = $this->language->get('error_price');
		}

		if (!is_numeric($post_info['offerprice']) || $post_info['offerprice'] <= 0) {
			$json['error']['offerprice'] = $this->language->get('error_price');
		}

		if (!oc_validate_length($post_info['ziptoship'], 5, 10)) {
			$json['error']['ziptoship'] = $this->language->get('error_ziptoship');
		}

		if (!oc_validate_length($post_info['name'], 3, 32)) {
			$json['error']['name'] = $this->language->get('error_name');
		}

		if (!oc_validate_email($post_info['email'])) {
			$json['error']['email'] = $this->language->get('error_email');
		}

		// Message is optional - no validation required

		// Captcha
		$this->load->model('setting/extension');

		$extension_info = $this->model_setting_extension->getExtensionByCode('captcha', $this->config->get('config_captcha'));

		if ($extension_info && $this->config->get('captcha_' . $this->config->get('config_captcha') . '_status') && in_array('makeoffer', (array)$this->config->get('config_captcha_page'))) {
			$captcha = $this->load->controller('extension/' . $extension_info['extension'] . '/captcha/' . $extension_info['code'] . '.validate');

			if ($captcha) {
				$json['error']['captcha'] = $captcha;
			}
		}

		if (!$json) {
			if ($this->config->get('config_mail_engine')) {
				$mail_option = [
					'parameter'     => $this->config->get('config_mail_parameter'),
					'smtp_hostname' => $this->config->get('config_mail_smtp_hostname'),
					'smtp_username' => $this->config->get('config_mail_smtp_username'),
					'smtp_password' => html_entity_decode($this->config->get('config_mail_smtp_password'), ENT_QUOTES, 'UTF-8'),
					'smtp_port'     => $this->config->get('config_mail_smtp_port'),
					'smtp_timeout'  => $this->config->get('config_mail_smtp_timeout')
				];

				$mail = new \Opencart\System\Library\Mail($this->config->get('config_mail_engine'), $mail_option);
				$mail->setTo($this->config->get('config_email'));
				$mail->setFrom($this->config->get('config_email'));
				$mail->setReplyTo($post_info['email']);
				$mail->setSender(html_entity_decode($post_info['name'], ENT_QUOTES, 'UTF-8'));
				$mail->setSubject(html_entity_decode(sprintf($this->language->get('email_subject'), $post_info['name']), ENT_QUOTES, 'UTF-8'));
				
				$message = sprintf($this->language->get('email_message'),
					$post_info['itemdescription'],
					$this->currency->format($post_info['currentprice'], $this->config->get('config_currency')),
					$this->currency->format($post_info['offerprice'], $this->config->get('config_currency')),
					$post_info['ziptoship'],
					$post_info['name'],
					$post_info['email'],
					$post_info['message']
				);

				$mail->setText($message);
				$mail->send();
			}

			$json['redirect'] = $this->url->link('information/makeoffer.success', 'language=' . $this->config->get('config_language'), true);
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

	public function autocomplete(): void {
		$json = [];

		try {
			if (isset($this->request->get['filter_name'])) {
				$this->load->model('catalog/product');
				
				$filter_data = [
					'filter_search' => $this->request->get['filter_name'],
					'start'       => 0,
					'limit'       => 5,
					'filter_status' => 1
				];

				$results = $this->model_catalog_product->getProducts($filter_data);

				foreach ($results as $result) {
					$json[] = [
						'name' => html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8'),
						'product_id' => $result['product_id']
					];
				}
			}

			$this->response->addHeader('Content-Type: application/json');
			$this->response->setOutput(json_encode($json));
		} catch (Exception $e) {
			$this->log->write('Autocomplete Error: ' . $e->getMessage());
			$this->response->addHeader('Content-Type: application/json');
			$this->response->setOutput(json_encode(['error' => 'An error occurred']));
		}
	}

	public function success(): void {
		$this->load->language('information/makeoffer');

		$this->document->setTitle($this->language->get('heading_title'));

		$data['breadcrumbs'] = [];

		$data['breadcrumbs'][] = [
			'text' => $this->language->get('text_home'),
			'href' => $this->url->link('common/home', 'language=' . $this->config->get('config_language'))
		];

		$data['breadcrumbs'][] = [
			'text' => $this->language->get('heading_title'),
			'href' => $this->url->link('information/makeoffer', 'language=' . $this->config->get('config_language'))
		];

		$data['text_message'] = $this->language->get('text_message');

		$data['continue'] = $this->url->link('common/home', 'language=' . $this->config->get('config_language'));

		$data['column_left'] = $this->load->controller('common/column_left');
		$data['column_right'] = $this->load->controller('common/column_right');
		$data['content_top'] = $this->load->controller('common/content_top');
		$data['content_bottom'] = $this->load->controller('common/content_bottom');
		$data['footer'] = $this->load->controller('common/footer');
		$data['header'] = $this->load->controller('common/header');

		$this->response->setOutput($this->load->view('common/success', $data));
	}
}