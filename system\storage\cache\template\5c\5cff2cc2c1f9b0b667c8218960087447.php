<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* extension/featured_categories/catalog/view/template/module/featured_categories.twig */
class __TwigTemplate_3be973f7d00b74affd9a6e8ed01bf41f extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 1
        yield "<h2 class=\"lined-heading\">";
        yield ($context["heading_title"] ?? null);
        yield "</h2>
<div class=\"row featured-categories slide-fadein\">
  ";
        // line 3
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(($context["categories"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["category"]) {
            // line 4
            yield "    <div class=\"col-md-3 mb-4\">
      <div class=\"card h-100\">
        <div class=\"card-body text-center\">
          <img src=\"";
            // line 7
            yield CoreExtension::getAttribute($this->env, $this->source, $context["category"], "image", [], "any", false, false, false, 7);
            yield "\" alt=\"";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 7);
            yield "\" class=\"card-img-top\" />
          <a href=\"";
            // line 8
            yield CoreExtension::getAttribute($this->env, $this->source, $context["category"], "href", [], "any", false, false, false, 8);
            yield "\" class=\"btn btn-primary my-3\">";
            yield ($context["text_view_products"] ?? null);
            yield "</a>
          <h3 class=\"card-title\">";
            // line 9
            yield CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 9);
            yield "</h3>
        </div>
      </div>
    </div>
  ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['category'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 14
        yield "</div>
";
        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "extension/featured_categories/catalog/view/template/module/featured_categories.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  80 => 14,  69 => 9,  63 => 8,  57 => 7,  52 => 4,  48 => 3,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<h2 class=\"lined-heading\">{{ heading_title }}</h2>
<div class=\"row featured-categories slide-fadein\">
  {% for category in categories %}
    <div class=\"col-md-3 mb-4\">
      <div class=\"card h-100\">
        <div class=\"card-body text-center\">
          <img src=\"{{ category.image }}\" alt=\"{{ category.name }}\" class=\"card-img-top\" />
          <a href=\"{{ category.href }}\" class=\"btn btn-primary my-3\">{{ text_view_products }}</a>
          <h3 class=\"card-title\">{{ category.name }}</h3>
        </div>
      </div>
    </div>
  {% endfor %}
</div>
", "extension/featured_categories/catalog/view/template/module/featured_categories.twig", "D:\\wamp64\\www\\pex\\pondexpo\\extension\\featured_categories\\catalog\\view\\template\\module\\featured_categories.twig");
    }
}
