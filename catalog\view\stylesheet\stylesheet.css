html {
  overflow-x: hidden;
}
  body {
  width: 100%;
  height: 100%;
}
h1, h2, h3, h4, h5, h6 {
  color: #444;
}
legend {
  padding: 7px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #e5e5e5;
}
label {
  font-size: 12px;
  font-weight: normal;
}
/* Chrome border line */
button:focus {
  outline: none !important;
}
/* container */
#container {
  width: 100%;
  min-height: 100%;
  position: absolute;
  margin-bottom: 300px;
}
#alert {
  z-index: 9999;
  position: fixed;
  top: 30%;
  left: 50%;
  width: 400px;
  margin-left: -200px;
}
@media (min-width: 992px) {
  #alert {
    width: 600px;
    margin-left: -300px;
  }
}
@media (min-width: 1140px) {
  #alert {
    width: 600px;
    margin-left: -300px;
  }
}
@media (min-width: 1320px) {
  #alert {
    width: 600px;
    margin-left: -300px;
  }
}
#alert .alert {
  margin-bottom: 15px;
}
#alert .alert-primary {
  box-shadow: 0 0 0 5px rgb(var(--bs-primary-rgb), 0.1);
}
#alert .alert-secondary {
  box-shadow: 0 0 0 5px rgb(var(--bs-secondary-rgb), 0.1);
}
#alert .alert-success {
  box-shadow: 0 0 0 5px rgb(var(--bs-success-rgb), 0.1);
}
#alert .alert-warning {
  box-shadow: 0 0 0 5px rgb(var(--bs-warning-rgb), 0.1);
}
#alert .alert-danger {
  box-shadow: 0 0 0 5px rgb(var(--bs-danger-rgb), 0.1);
}
#alert .alert-info {
  box-shadow: 0 0 0 5px rgb(var(--bs-info-rgb), 0.1);
}
#alert .alert-light {
  box-shadow: 0 0 0 5px rgb(var(--bs-light-rgb), 0.1);
}
#alert .alert-dark {
  box-shadow: 0 0 0 5px rgb(var(--bs-dark-rgb), 0.1);
}
/* top */
#top {
  background-color: var(--bs-tertiary-bg);
  border-bottom: 1px solid var(--bs-border-color);
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 5px;
}
#top ul.list-inline {
  margin-bottom: 0;
}
#top .list-inline-item > a, #top .list-inline-item .dropdown > a {
  font-size: 1.1em;
  color: var(--bs-gray-600);
  line-height: 40px;
  vertical-align: middle;
  padding: 10px 0px 5px 0px;
}
/* logo */
#logo {
  text-align: center;
  margin: 7px 0 7px 0;
}
#logo img {
  max-width: 200px;
}
@media (min-width: 768px) {
  #logo {
    text-align: left;
  }
}
/* search */
#search {
  margin-bottom: 10px;
}
#search .form-control-lg {
  height: 40px;
  font-size: 12px;
  line-height: 20px;
  padding: 0 10px;
}
#search .btn-lg {
  font-size: 15px;
  line-height: 18px;
  padding: 0.57rem 35px;
  text-shadow: 0 1px 0 #FFF;
}
/* cart */
#cart {
  margin-bottom: 10px;
}
#cart .img-thumbnail {
  min-width: 100px;
}
#cart .btn-lg {
  font-size: 15px;
  line-height: 18px;
  padding: 14px 35px;
}
#cart .dropdown-menu {
  background: #eee;
}
#cart .dropdown-menu li {
  min-width: 300px;
}
@media (max-width: 768px) {
  #cart .dropdown-menu li {
    min-width: 100%;
  }
}
/* menu */
#menu {
  background-color: #229ac8;
  background-image: linear-gradient(to bottom, #23a1d1, #1f90bb);
  background-repeat: repeat-x;
  border: 1px solid #1f90bb;
  border-color: #1f90bb #1f90bb #145e7a;
  min-height: 40px;
  border-radius: 4px;
  padding: 0 1rem;
  margin-bottom: 20px;
}
#menu .navbar-nav > li > a {
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  padding: 10px 15px 10px 15px;
  background-color: transparent;
}
#menu .navbar-nav > li > a:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
#menu .dropdown-menu {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
#menu .dropdown-inner {
  display: flex;
  flex-direction: column;
}
#menu .dropdown-inner ul {
  width: 100%;
  min-width: 200px;
}
@media (min-width: 960px) {
  #menu .dropdown:hover .dropdown-menu {
    display: block;
  }
  #menu .dropdown-inner {
    flex-direction: row;
  }
  #menu .nav-item + .nav-item + .nav-item .dropdown-column-3 {
    left: -200px;
  }
  #menu .nav-item + .nav-item + .nav-item .dropdown-column-4 {
    left: -400px;
  }
  #menu .nav-item + .nav-item + .nav-item + .nav-item .dropdown-column-2 {
    left: -200px;
  }
  #menu .nav-item + .nav-item + .nav-item + .nav-item .dropdown-column-3 {
    left: -400px;
  }
  #menu .nav-item + .nav-item + .nav-item + .nav-item .dropdown-column-4 {
    left: -600px;
  }
}
#category {
  float: left;
  font-size: 16px;
  font-weight: 700;
  line-height: 40px;
  color: #fff;
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
}
#menu .navbar-toggler i {
  color: #fff;
  border-color: #fff;
  font-size: 0.9em;
}
/* default boostrap changes */
div.required .col-form-label:before, div.required .form-label:before {
  content: "* ";
  color: #F00;
  font-weight: bold;
}
.form-switch-lg {
  font-size: 20px;
  min-height: 30px;
  line-height: 30px;
}
.nav-tabs {
  margin-bottom: 15px;
}
.form-check .form-check-input {
  margin-top: 0.25rem;
}
@media (min-width: 768px) {
  .col-form-label {
    text-align: right;
  }
}
/* breadcrumb */
.breadcrumb {
  margin: 0 0 20px 0;
  padding: 8px 0;
  border: 1px solid var(--bs-border-color);
  border-radius: var(--bs-border-radius);
  background-color: var(--bs-tertiary-bg);
}
.breadcrumb i {
  font-size: 15px;
}
.breadcrumb > li.breadcrumb-item {
  text-shadow: 0 1px 0 #FFF;
  padding: 0 20px;
  position: relative;
  white-space: nowrap;
}
.breadcrumb > li.breadcrumb-item > a {
  text-decoration: none;
}
.breadcrumb > li.breadcrumb-item:after {
  content: "";
  display: block;
  position: absolute;
  top: -3px;
  right: -5px;
  width: 29px;
  height: 29px;
  border-right: 1px solid var(--bs-border-color);
  border-bottom: 1px solid var(--bs-border-color);
  transform: rotate(-45deg);
}
.breadcrumb > li.breadcrumb-item + li:before {
  content: "";
  padding: 0;
}
.product-thumb {
  border: 1px solid #ddd;
  position: relative;
  height: 100%;
}
.product-thumb .image {
  text-align: center;
}
.product-thumb .image a:hover {
  opacity: 0.8;
}
.product-thumb .description {
  padding: 15px;
  margin-bottom: 45px;
}
.product-thumb .description h4 {
  font-weight: bold;
}
.product-thumb .button {
  display: flex;
  position: absolute;
  width: 100%;
  bottom: 0;
}
.product-thumb .button button {
  width: 33.33%;
  border: none;
  border-top: 1px solid var(--bs-border-color);
  background-color: var(--bs-tertiary-bg);
  color: var(--bs-gray-600);
  line-height: 38px;
  text-align: center;
}
.product-thumb .button button:hover {
  color: var(--bs-gray-600);
  background-color: #ddd;
  text-decoration: none;
  cursor: pointer;
}
.product-thumb .button button + button {
  border-left: 1px solid var(--bs-border-color);
}
@media (min-width: 960px) {
  .product-list .product-thumb {
    display: flex;
  }
  .product-list .product-thumb .image {
    flex-direction: column;
    margin-bottom: 0px;
  }
  .product-list .product-thumb .content {
    flex-direction: column;
    flex: 75%;
    position: relative;
  }
  .product-list .product-thumb .button {
    border-left: 1px solid #ddd;
    width: calc(100% -  15px);
    margin-left: 15px;
  }
}
.rating .fa-stack {
  width: 20px;
}
.rating .fa-star {
  color: #999;
  font-size: 15px;
}
.rating .fa-star {
  color: #FC0;
  font-size: 15px;
}
.rating .fa-star + .fa-star {
  color: #E69500;
}
/* product list */
.price {
  color: #444;
}
.price-new {
  color: var(--danger);
  font-weight: 600;
}
.price-old {
  text-decoration: line-through;
  text-decoration-color: #737d86;
}
.price-tax {
  color: #999;
  font-size: 12px;
  display: block;
}
/* blog */
.blog-thumb {
  border: 1px solid #ddd;
  margin-bottom: 15px;
}
.blog-thumb h4 {
  font-weight: bold;
}
.blog-thumb .image {
  text-align: center;
  margin-bottom: 15px;
}
.blog-thumb .image a:hover {
  opacity: 0.8;
}
.blog-thumb .description {
  padding: 15px;
}
/* Theme Custom CSS */
#cookie {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 150px;
  z-index: 9999;
  opacity: 0.95;
  color: #ecf0f1;
  background: #343a40;
}
#cookie div {
  font-size: 16px;
  color: #FFFFFF;
}
/*------------------------------------------------------*/
/* Custom Theme CSS - Customization starts here
/*------------------------------------------------------*/
/*------------------------------------------------------*/
/* Color Variables
/*------------------------------------------------------*/
:root {
  --site-background: #fff;
  --textcolor: #666;
  --headingcolor: #454545;

  --primary: #0f61ae;
  --lightprimary: #43bee8;
  --ascent: #66cc00;

  --secondary: #003366;
  --danger: #D10000;

  --bgheader: #fff;
  --bgbreadcrumb: #eee;

  --smoke: #F5F5F5;
  --dark-smoke: #d6d7d8;
  --border: #eee;
  --dark-border: #b9b9b9;
  --grey: #dddddd;
  --dark-grey: #a3a3a3;
  --charcoal: #555555;
  --dark-charcoal: #454545;
  --black-charcoal: #333333;
  --pitch-black: #111111;

  --bs-danger-rgb: 209, 0, 0;
  --bs-focus-ring-width: 2px; /* Adjust the ring width */
       --bs-focus-ring-color: rgb(166, 255, 0); /* Replace 'your-color' with your desired color */
       --bs-focus-ring-opacity: 0.5; /* Adjust the opacity */

  /* For White and Black we use the name or hex values directly instead of variables. */
}
.form-control:focus,
.accordion-button:focus {
  box-shadow: 0 0 0 0.2rem rgba(240, 165, 0, .2); /* Replace 'your-color' with your desired color */
  border-color: var(--primary);
}
/*------------------------------------------------------*/
/* bootstrap changes - Optional */
/*------------------------------------------------------*/
@media (min-width: 1400px) {

  .container,
  .container-sm,
  .container-md,
  .container-lg,
  .container-xl,
  .container-xxl {
    max-width: 1320px;
  }
}

@media (min-width: 1600px) {

  .container,
  .container-sm,
  .container-md,
  .container-lg,
  .container-xl,
  .container-xxl {
    max-width: 1440px;
  }
}

.container-small {
  max-width: 540px;
  margin: 0 auto;
}

.container-medium {
  max-width: 720px;
  margin: 0 auto;
}

.container-large {
  max-width: 960px;
  margin: 0 auto;
}

.container-xlarge {
  max-width: 1140px;
  margin: 0 auto;
}

.container-xxlarge {
  max-width: 1320px;
  margin: 0 auto;
}

.container-fluid {
  width: 100%;
  padding: 0 15px;
}

.container-full {
  width: 100%;
  padding: 0;
}
/*------------------------------------------------------*/
/* General CSS 
/*------------------------------------------------------*/
body {
  font-family: 'Raleway', Arial, sans-serif;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.62;
  width: 100%;
  height: 100%;
  color: var(--textcolor);
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  background-color: var(--site-background);
}

@media (max-width: 992px) {
  body {
    font-size: 16px;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--headingcolor);
}

legend {
  color: var(--headingcolor);
  font-family: "Raleway", sans-serif;
  padding: 7px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--dark-grey);
}

a {
  color: var(--primary);
  text-decoration: none;
}
hr {
    margin: 24px 0;
}
.border {
  border: 1px solid  var(--bs-border-color) !important;
  background-color: var(--bs-tertiary-bg);
}

.border-p5 {
  border: 1px solid var var(--bs-border-color) !important;
  padding: 5px;
}
.border-p10 {
  border: 1px solid var var(--bs-border-color) !important;
  background-color: var(--bs-tertiary-bg);
  padding: 10px;
}
.blink {
  animation: blinker 1s step-start infinite;
}
@keyframes blinker {
  50% {
    opacity: 0;
  }
}
.alignright {
  float: right;
  margin: 0 0 20px 20px;
}
.alignleft {
  float: left;
  margin: 0 20px 20px 0;
}
.red {
  color: #D10000;
}
.green {
  color: #047e00;
}
.primary {
  color: var(--primary);
}
/*------------------------------------------------------*/
/* Topbar
/*------------------------------------------------------*/
.topbar {
  background-color: var(--secondary);
  color: #fff;
  line-height: 40px;
  font-size: 18px;
}
.topbar ul {
  margin: 0;
  padding: 0;
}
.topbar li a {
  color: #fff;
  text-decoration: none;
}
.btnoffer {
  text-align: right;
}
.topcontact li a:hover {
  color: var(--ascent);
  text-decoration: none;
}
.scroller {
  overflow: hidden;
  margin-top: 15px;
  font-size: 16px;
}
/*------------------------------------------------------*/
/* Module Heading Home page
/*------------------------------------------------------*/
.lined-heading {
  text-align: center;
  margin-top: 50px;
  margin-bottom: 20px;
}
.lined-heading-wmt {
  text-align: center;
  margin-bottom: 20px;
}

.lined-heading::before,
.lined-heading::after,
.lined-heading-wmt::before,
.lined-heading-wmt::after {
  content: "";
  vertical-align: top;
  display: inline-block;
  width: 54px;
  height: 0.65em;
  border-bottom: 3px solid var(--primary);
  margin: 0 15px 0 -10%;
}

.lined-heading::after,
.lined-heading-wmt::after {
  margin: 0 -10% 0 15px;
}

.module-subheading {
  text-align: center;
  margin-bottom: 30px;
  font-style: italic;
}
.labelheading {
  text-align: center;
  margin-top: 30px;
}
.labelheading h3 {
  font-size: 18px;
}
/*------------------------------------------------------*/
/* Buttons
/*------------------------------------------------------*/
.btn-offer {
  color: var(--black-charcoal);
  font-weight: bold;
  font-size: 13px;
  background-color: var(--bs-warning);
  border-color: var(--bs-warning) var(--bs-warning) var(--bs-warning);
  padding: 5px 10px;
  border-radius: 4px;
}

.btn-offer:hover,
.btn-offer:focus,
.btn-offer:active,
.btn-offer.active,
.btn-offer.disabled,
.btn-offer[disabled] {
  color: white;
  background-color: var(--primary);
  border-color: var(--primary);
  box-shadow: 0 0 4px var(--primary);
}
.btn-primary {
  color: white;
  font-weight: bold;
  font-size: 13px;
  background-color: var(--primary);
  border-color: var(--primary) var(--primary) var(--primary);
  padding: 5px 10px;
  border-radius: 4px;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.btn-primary.disabled,
.btn-primary[disabled] {
  color: white;
  background-color: var(--secondary);
  border-color: var(--secondary);
  box-shadow: 0 0 4px var(--secondary);
}
/*---------------------------------------------*/
/* Discount / Special Badge */
/*---------------------------------------------*/
.forsalebadge {
  position: relative;
}
.discount {
  position: absolute;
  display: inline-block;
  background: #047e00;
  color: #fff;
  height: 24px;
  width: 24px;
  text-align: center;
  vertical-align: middle;
  line-height: 27px;
  top: 7px;
  left: 7px;
  font-size: 18px;
  line-height: 24px;
  transform: rotate(-30deg);
  z-index: 2;

  &:before,
  &:after {
    content: "%";
    position: absolute;
    background: inherit;
    height: inherit;
    width: inherit;
    top: 0;
    left: 0;
  }

  &:after {
    transform: rotate(30deg);
  }

  &:before {
    transform: rotate(60deg);
  }
}
/*---------------------------------------------*/
/* Sale Badge
/*---------------------------------------------*/
.salebadge {
  position: absolute;
  display: inline-block;
  background: #D10000;
  height: 24px;
  width: 24px;
  text-align: center;
  vertical-align: middle;
  line-height: 27px;
  top: 7px;
  left: 7px;
  font-weight: bold;
  font-size: 9px;
  line-height: 24px;
  transform: rotate(-30deg);
  z-index: 10;

  &:before,
  &:after {
    content: "SALE";
    position: absolute;
    background: inherit;
    height: inherit;
    width: inherit;
    top: 0;
    left: 0;
    color: white;
  }

  &:after {
    transform: rotate(30deg);
  }

  &:before {
    transform: rotate(60deg);
  }
}

.salebadge span {
  position: absolute;
  font-size: 10px;
  color: white;
  z-index: 101;
  right: 0;
}
.salebadge-prod {
  position: absolute;
  display: inline-block;
  background: #D10000;
  height: 24px;
  width: 24px;
  text-align: center;
  vertical-align: middle;
  line-height: 27px;
  top: 10px;
  right: 50px;
  font-weight: bold;
  font-size: 9px;
  line-height: 24px;
  transform: rotate(-30deg);
  z-index: 10;

  &:before,
  &:after {
    content: "SALE";
    position: absolute;
    background: inherit;
    height: inherit;
    width: inherit;
    top: 0;
    left: 0;
    color: white;
  }

  &:after {
    transform: rotate(30deg);
  }

  &:before {
    transform: rotate(60deg);
  }
}

.salebadge-prod span {
  position: absolute;
  font-size: 10px;
  color: white;
  z-index: 101;
  right: 0;
}
/*------------------------------------------------------*/
/* Header
/*------------------------------------------------------*/
.bgheader {
  background-color: var(--bgheader);
}
#logo {
  margin: 10px 0;
}
#logo img {
  max-width: 100%;
  height: auto;
}
/*------------------------------------------------------*/
/* Account Menu
/*------------------------------------------------------*/
.account-links-top {
  text-align: center;
  font-weight: 500;
  margin-top: 5px;
}
#top {
  background-color: transparent;
  position: relative;
  margin-bottom: 0;
  border-bottom: 0;
  padding-bottom: 0;
  font-size: 13px;
}
@media (min-width: 768px) {
  .account-links-top {
    text-align: right;
  }
}
#top ul.list-inline {
  margin-bottom: 0;
}
#top a, #top .list-inline-item > a, #top .list-inline-item .dropdown > a {
  font-size: 13px;
  color: var(--dark-charcoal);
  line-height: 30px;
  vertical-align: middle;
}
#top .list-inline-item > a:hover, #top .list-inline-item .dropdown > a:hover {
  color: black;
}
#top .dropdown-item:hover {
  color: var(--smoke);
  background-color: var(--secondary);
}
.topcart {
  margin-left: 10px;
}
.topcart .btn-light {
  background-color: var(--grey);
  color: black;
  border: 0;
}
.topcart .btn-light:hover {
  background-color: var(--primary);
  color: white;
}
.topcart .btn-light:focus {
  background-color: var(--primary);
  color: white;
}
#cart {
  margin-bottom: 0;
}
#cart .dropdown-menu {
  background: var(--smoke);
  color: white;
}
/* Search Modal Popup */
.searchbox {
  padding: 20px 20px 0 20px;
}
.btn-light:hover {
  color: var(--primary);
}
/*------------------------------------------------------*/
/* Top Menu
/*------------------------------------------------------*/
.mainmenu {
  background-color: var(--primary);
  border: 0;
  min-height: 40px;
  padding: 0;
}
.mainmenu .container {
  padding: 0;
}
#menu {
  background-color: var(--primary);
  background-image: none;
  border: 0;
  min-height: 40px;
  border-radius: 0;
  padding: 0 1rem;
  margin-bottom: 0;
}
#menu .navbar-nav > li > a {
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  padding: 10px 18px 10px 18px;
  background-color: transparent;
  font-family: 'Poppins', Arial, sans-serif;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
}
#menu .navbar-nav > li > a:hover {
  color: var(--ascent);
  background-color: var(--secondary);
}
.dropdown-menu {
  --bs-dropdown-padding-y: 0;
  --bs-dropdown-divider-margin-y: 0;
  font-size: 14px;
}
.dropdown-item {
  padding: 10px 15px;
}
.dropdown-item:hover {
  color: var(--smoke);
  background-color: var(--secondary);
}
/*------------------------------------------------------*/
/* Banner Home
/*------------------------------------------------------*/
.bannerhome {
  position: relative;}
.bannerhome img {
  width: 100%;
  height: auto;
}
.bannerhome .carousel {
  border: 0;
  border-bottom: 1px solid var(--dark-grey);
  margin-bottom: 0;
}
/* Shine Effect */
.shine {
  position: relative;
  overflow: hidden;
}

.shine::before {
  background: linear-gradient(to right,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.1) 100%);
  content: "";
  display: block;
  height: 100%;
  left: -75%;
  position: absolute;
  top: 0;
  transform: skewX(-25deg);
  width: 50%;
  z-index: 2;
}

.shine:hover::before,
.shine:focus::before {
  -webkit-animation: shine 1.2s;
  animation: shine 1.2s;
}

@-webkit-keyframes shine {
  100% {
    left: 125%;
  }
}

@keyframes shine {
  100% {
    left: 125%;
  }
}
/*------------------------------------------------------*/
/* Blue Text Box
/*------------------------------------------------------*/
.bluebox1 {
  background-color: var(--primary);
  color: #fff;
  padding: 38px 30px 30px 30px;
  text-align: center;
}
.bluebox1 h2,
.bluebox1 h3,
.bluebox1 h4 {
  color: #fff;
}
.bluebox2 {
  background-color: var(--primary);
  color: #fff;
  padding: 39px 30px 30px 30px;
  text-align: center;
  margin-top: 42px;
  margin-bottom: 50px;
}
.bluebox2 h2,
.bluebox2 h3,
.bluebox2 h4 {
  color: #fff;
}
.bluebox3 {
  background-color: var(--primary);
  color: #fff;
  padding: 39px 30px 30px 30px;
  text-align: center;
  margin-top: 42px;
}
.bluebox3 h2,
.bluebox3 h3,
.bluebox3 h4 {
  color: #fff;
}
/*------------------------------------------------------*/
/* Category page
/*------------------------------------------------------*/
.breadcrumb {
  margin: 20px 0;
}
.breadcrumb > li.breadcrumb-item {
  text-shadow: none;
  font-size: 14px;
}
.breadcrumb > li.breadcrumb-item:after {
  border-right: 1px solid var(--dark-smoke);
  border-bottom: 1px solid var(--dark-smoke);
  width: 27px;
  height: 27px;
}
/* Category page Left menu */
/* list group */
.list-group a {
  color: var(--primary);
  padding: 10px 12px;
  text-shadow: none;
  font-size: 14px;
}

.list-group a.active:hover,
.list-group a:hover {
  color: white;
  background: var(--secondary);
  text-shadow: none;
}

.list-group a.active {
  color: white;
  background: var(--primary);
  border: 1px solid var(--smoke);
  text-shadow: none;
}
.refinesearch a {
  font-size: 14px;
  border: 1px solid var(--primary);
  border-radius: 4px;
  padding: 3px 7px;
  background-color: var(--smoke);
  font-weight: 500;
}
.refinesearch a:hover {
  background-color: var(--dark-smoke);
}
/*------------------------------------------------------*/
/* Product Box
/*------------------------------------------------------*/
.product-thumb,
.blog-thumb,
.featured-box {
  border: 1px solid #ddd;
  position: relative;
  text-align: center;
  height: 100%;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
}
.product-thumb:hover,
.blog-thumb:hover,
.featured-box:hover {
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}
.blog-thumb .description {
  padding: 15px 15px 0 15px;
}
.product-thumb .button button {
  width: 25%;
  border: none;
  border-top: 1px solid var(--bs-border-color);
  background-color: var(--bs-tertiary-bg);
  color: var(--bs-gray-600);
  line-height: 38px;
  text-align: center;
}
.product-thumb .button button.long {
  width: 50%;
  font-weight: 600;
}
.product-thumb .button button:hover {
  color: var(--primary);
}
.featured-categories .card {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
}
.featured-categories .card:hover {
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}
/*------------------------------------------------------*/
/* Make Offer
/*------------------------------------------------------*/
.makeofferform {
  padding: 10px;
  background-color: #eee;
}

.makeofferform form {
  width: 96%;
  margin-right: auto;
  margin-left: auto;
}
/*------------------------------------------------------*/
/* Testimonials
/*------------------------------------------------------*/
.testimonial-carousel .carousel {
  margin-bottom: 0;
}
.testimonial-content {
  font-size: 18px;
  color: #666;
  text-align: center;
  padding: 30px;
}
.testimonial-content-page {
  text-align: left;
  padding: 20px;
}
/*------------------------------------------------------*/
/* Product Page
/*------------------------------------------------------*/
.accordion-button:not(.collapsed) {
  background-color: var(--primary);
  color: white;
}
.accordion-button:hover {
  background-color: var(--lightprimary);
}
.accordion-button {
  background-color: var(--smoke);
  font-size: 14px;
}
.prodinfo img {
  max-width: 100%;
  height: auto;
}
.prodoptions label,
.prodoptions select {
  font-size: 14px;
}
.qtybox {
  margin-top: 20px;
  font-size: 14px;
}
.qtybox input[type=number] {
  font-size: 14px;
}
.form-check-input[type="radio"] {
    border-color: var(--primary);
}
.product-enquiry {
  background-color: #f8f9fa;
  padding: 10px;
  border: 1px solid var(--bs-border-color);
  border-radius: 4px;
}
.mt-10 {
  margin-top: 160px;
}
/*---------------------------------------------*/
/* Thumbnails
/*---------------------------------------------*/
.img-thumbnail:hover {
  border: var(--bs-border-width) solid var(--primary);
}

.thumbnails img {
  padding: 10px;
  margin-bottom: 5px;
  max-width: 120px;
}

@media (min-width: 1201px) and (max-width: 1366px) {
  .thumbnails img {
    max-width: 96px;
  }
}

@media (min-width: 990px) and (max-width: 1200px) {
  .thumbnails img {
    max-width: 75px;
  }
}
/*---------------------------------------------*/
/* Quantity Field Changes
/*---------------------------------------------*/
input[type="number"]::-webkit-inner-spin-button {
  opacity: 1;
}
/*------------------------------------------------------*/
/* Footer
/*------------------------------------------------------*/
footer {
  color: var(--dark-smoke);
}

footer h5 {
  font-family: "Raleway", sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: var(--headingcolor);
  margin-top: 10px;
  margin-bottom: 10px;
}

.prefooter {
  padding-top: 42px;
  padding-bottom: 20px;
  background: var(--smoke);
  margin-top: 30px;
}

.prefooter a {
  color: var(--text);
}

.prefooter li {
  line-height: 30px;
  font-size: 13px;
  margin-left: 0;
  transition: all 0.5s ease;
  color: var(--textcolor);
}

.prefooter li:hover {
  margin-left: 10px;
  list-style-type: disc;
  color: var(--black-charcoal);
}

.prefooter li::marker {
  color: var(--black-charcoal);
}

.prefooter h5:after {
  content: "";
  /* This is necessary for the pseudo element to work. */
  display: block;
  /* This will put the pseudo element on its own line. */
  width: 10%;
  /* Change this to whatever width you want. */
  padding-top: 7px;
  /* This creates some space between the element and the border. */
  border-bottom: 3px solid var(--primary);
  /* This creates the border. Replace black with whatever color you want. */
}

.prefooter p {
  color: var(--textcolor);
  margin-bottom: 9px;
}
.footerlogo {
  margin-bottom: 15px;
  max-width: 200px;
}

.footermain {
  background-color: var(--secondary);
  line-height: 42px;
  font-size: 12px;
  padding-top: 2px;
}

.footermain a {
  color: var(--smoke);
}

.footermain a:hover {
  color: var(--primary);
}

.footermain p {
  margin-bottom: 0;
  color: var(--smoke);
}

.footer-icons .fa-brands {
  font-size: 16px;
  line-height: 42px;
}

.footer-icons i {
  margin: 0 10px;
}

.designby {
  text-align: right;
}

@media (max-width: 960px) {

  .designby,
  .copyright {
    text-align: center;
  }
  .footer-icons .fa-brands {
    font-size: 24px;
  }
}

/*------------------------------------------------------*/
/* Go to Top
/*------------------------------------------------------*/
.gototop {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  position: fixed;
  bottom: 30px;
  right: 20px;
  display: none;
  background: var(--primary);
  z-index: 1000;
}

.gototop .fa-solid {
  color: #fff;
  font-size: 16px;
  display: flex;
  justify-content: center;
  margin-top: 5px;
}
/*------------------------------------------------------*/
/* Mobile Specefic
/*------------------------------------------------------*/
@media (max-width: 768px) {
  .topbar {
    font-size: 14px;
  }
  .account-links-top {
    text-align: center;
    margin-top: 0;
  }
  #top .list-inline-item > a .fa-solid {
    font-size: 14px;
  }
  #logo {
    margin-bottom: 0;
  }
}

.option-image img {
  max-width: 100px;
  height: auto;
}

.respimage img {
  max-width: 100% !important;
  height: auto;
}


.order-success {
  border: 2px solid var(--primary);
  text-align: center;
  padding: 10px;
  max-width: 450px;
  margin: 30px auto;
  font-size: 16px;
}