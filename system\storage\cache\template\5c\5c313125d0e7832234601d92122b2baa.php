<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* catalog/view/template/mail/order_alert.twig */
class __TwigTemplate_6e6b80230e50d6e2929a7a6023dd1809 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 1
        yield ($context["text_received"] ?? null);
        yield "<br/>
<br/>
<strong>";
        // line 3
        yield ($context["text_order_id"] ?? null);
        yield "</strong> ";
        yield ($context["order_id"] ?? null);
        yield "<br/>
<strong>";
        // line 4
        yield ($context["text_date_added"] ?? null);
        yield "</strong> ";
        yield ($context["date_added"] ?? null);
        yield "<br/>
<strong>";
        // line 5
        yield ($context["text_order_status"] ?? null);
        yield "</strong> ";
        yield ($context["order_status"] ?? null);
        yield "<br/>
<br/>
<strong>";
        // line 7
        yield ($context["text_customer_details"] ?? null);
        yield "</strong><br/>
";
        // line 8
        yield ($context["text_customer_name"] ?? null);
        yield " ";
        yield ($context["customer_name"] ?? null);
        yield "<br/>
";
        // line 9
        yield ($context["text_customer_email"] ?? null);
        yield " ";
        yield ($context["customer_email"] ?? null);
        yield "<br/>
";
        // line 10
        if (($context["customer_telephone"] ?? null)) {
            // line 11
            yield ($context["text_customer_telephone"] ?? null);
            yield " ";
            yield ($context["customer_telephone"] ?? null);
            yield "<br/>
";
        }
        // line 13
        yield "<br/>
<strong>";
        // line 14
        yield ($context["text_payment_address"] ?? null);
        yield "</strong><br/>
";
        // line 15
        yield ($context["payment_firstname"] ?? null);
        yield " ";
        yield ($context["payment_lastname"] ?? null);
        yield "<br/>
";
        // line 16
        if (($context["payment_company"] ?? null)) {
            yield ($context["payment_company"] ?? null);
            yield "<br/>";
        }
        // line 17
        yield ($context["payment_address_1"] ?? null);
        yield "<br/>
";
        // line 18
        if (($context["payment_address_2"] ?? null)) {
            yield ($context["payment_address_2"] ?? null);
            yield "<br/>";
        }
        // line 19
        yield ($context["payment_city"] ?? null);
        yield " ";
        yield ($context["payment_postcode"] ?? null);
        yield "<br/>
";
        // line 20
        yield ($context["payment_zone"] ?? null);
        yield "<br/>
";
        // line 21
        yield ($context["payment_country"] ?? null);
        yield "<br/>
<br/>
";
        // line 23
        if ((($context["shipping_firstname"] ?? null) && (((($context["shipping_firstname"] ?? null) != ($context["payment_firstname"] ?? null)) || (($context["shipping_lastname"] ?? null) != ($context["payment_lastname"] ?? null))) || (($context["shipping_address_1"] ?? null) != ($context["payment_address_1"] ?? null))))) {
            // line 24
            yield "<strong>";
            yield ($context["text_shipping_address"] ?? null);
            yield "</strong><br/>
";
            // line 25
            yield ($context["shipping_firstname"] ?? null);
            yield " ";
            yield ($context["shipping_lastname"] ?? null);
            yield "<br/>
";
            // line 26
            if (($context["shipping_company"] ?? null)) {
                yield ($context["shipping_company"] ?? null);
                yield "<br/>";
            }
            // line 27
            yield ($context["shipping_address_1"] ?? null);
            yield "<br/>
";
            // line 28
            if (($context["shipping_address_2"] ?? null)) {
                yield ($context["shipping_address_2"] ?? null);
                yield "<br/>";
            }
            // line 29
            yield ($context["shipping_city"] ?? null);
            yield " ";
            yield ($context["shipping_postcode"] ?? null);
            yield "<br/>
";
            // line 30
            yield ($context["shipping_zone"] ?? null);
            yield "<br/>
";
            // line 31
            yield ($context["shipping_country"] ?? null);
            yield "<br/>
<br/>
";
        }
        // line 34
        if (($context["payment_method"] ?? null)) {
            // line 35
            yield "<strong>";
            yield ($context["text_payment_method"] ?? null);
            yield "</strong> ";
            yield ($context["payment_method"] ?? null);
            yield "<br/>
";
        }
        // line 37
        if (($context["shipping_method"] ?? null)) {
            // line 38
            yield "<strong>";
            yield ($context["text_shipping_method"] ?? null);
            yield "</strong> ";
            yield ($context["shipping_method"] ?? null);
            yield "<br/>
";
        }
        // line 40
        yield "<br/>
<strong>";
        // line 41
        yield ($context["text_product"] ?? null);
        yield "</strong><br/>
<br/>
";
        // line 43
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(($context["products"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["product"]) {
            // line 44
            yield CoreExtension::getAttribute($this->env, $this->source, $context["product"], "quantity", [], "any", false, false, false, 44);
            yield "x ";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["product"], "name", [], "any", false, false, false, 44);
            yield " (";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["product"], "model", [], "any", false, false, false, 44);
            yield ") ";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["product"], "total", [], "any", false, false, false, 44);
            yield "<br/>
";
            // line 45
            if (CoreExtension::getAttribute($this->env, $this->source, $context["product"], "option", [], "any", false, false, false, 45)) {
                // line 46
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["product"], "option", [], "any", false, false, false, 46));
                foreach ($context['_seq'] as $context["_key"] => $context["option"]) {
                    // line 47
                    yield "\t- ";
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "name", [], "any", false, false, false, 47);
                    yield " ";
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "value", [], "any", false, false, false, 47);
                    yield "<br/>
";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['option'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
            }
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['product'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 51
        yield "<br/>
<strong>";
        // line 52
        yield ($context["text_total"] ?? null);
        yield "</strong><br/>
<br/>
";
        // line 54
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(($context["totals"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["total"]) {
            // line 55
            yield CoreExtension::getAttribute($this->env, $this->source, $context["total"], "title", [], "any", false, false, false, 55);
            yield ": ";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["total"], "value", [], "any", false, false, false, 55);
            yield "<br/>
";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['total'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 57
        yield "<br/>
";
        // line 58
        if (($context["comment"] ?? null)) {
            // line 59
            yield "<strong>";
            yield ($context["text_comment"] ?? null);
            yield "</strong><br/>
<br/>
";
            // line 61
            yield ($context["comment"] ?? null);
            yield "<br/>
<br/>
";
        }
        // line 64
        yield ($context["store"] ?? null);
        yield "<br/>
";
        // line 65
        yield ($context["store_url"] ?? null);
        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "catalog/view/template/mail/order_alert.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  283 => 65,  279 => 64,  273 => 61,  267 => 59,  265 => 58,  262 => 57,  252 => 55,  248 => 54,  243 => 52,  240 => 51,  224 => 47,  220 => 46,  218 => 45,  208 => 44,  204 => 43,  199 => 41,  196 => 40,  188 => 38,  186 => 37,  178 => 35,  176 => 34,  170 => 31,  166 => 30,  160 => 29,  155 => 28,  151 => 27,  146 => 26,  140 => 25,  135 => 24,  133 => 23,  128 => 21,  124 => 20,  118 => 19,  113 => 18,  109 => 17,  104 => 16,  98 => 15,  94 => 14,  91 => 13,  84 => 11,  82 => 10,  76 => 9,  70 => 8,  66 => 7,  59 => 5,  53 => 4,  47 => 3,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{{ text_received }}<br/>
<br/>
<strong>{{ text_order_id }}</strong> {{ order_id }}<br/>
<strong>{{ text_date_added }}</strong> {{ date_added }}<br/>
<strong>{{ text_order_status }}</strong> {{ order_status }}<br/>
<br/>
<strong>{{ text_customer_details }}</strong><br/>
{{ text_customer_name }} {{ customer_name }}<br/>
{{ text_customer_email }} {{ customer_email }}<br/>
{% if customer_telephone %}
{{ text_customer_telephone }} {{ customer_telephone }}<br/>
{% endif %}
<br/>
<strong>{{ text_payment_address }}</strong><br/>
{{ payment_firstname }} {{ payment_lastname }}<br/>
{% if payment_company %}{{ payment_company }}<br/>{% endif %}
{{ payment_address_1 }}<br/>
{% if payment_address_2 %}{{ payment_address_2 }}<br/>{% endif %}
{{ payment_city }} {{ payment_postcode }}<br/>
{{ payment_zone }}<br/>
{{ payment_country }}<br/>
<br/>
{% if shipping_firstname and (shipping_firstname != payment_firstname or shipping_lastname != payment_lastname or shipping_address_1 != payment_address_1) %}
<strong>{{ text_shipping_address }}</strong><br/>
{{ shipping_firstname }} {{ shipping_lastname }}<br/>
{% if shipping_company %}{{ shipping_company }}<br/>{% endif %}
{{ shipping_address_1 }}<br/>
{% if shipping_address_2 %}{{ shipping_address_2 }}<br/>{% endif %}
{{ shipping_city }} {{ shipping_postcode }}<br/>
{{ shipping_zone }}<br/>
{{ shipping_country }}<br/>
<br/>
{% endif %}
{% if payment_method %}
<strong>{{ text_payment_method }}</strong> {{ payment_method }}<br/>
{% endif %}
{% if shipping_method %}
<strong>{{ text_shipping_method }}</strong> {{ shipping_method }}<br/>
{% endif %}
<br/>
<strong>{{ text_product }}</strong><br/>
<br/>
{% for product in products %}
{{ product.quantity }}x {{ product.name }} ({{ product.model }}) {{ product.total }}<br/>
{% if product.option %}
{% for option in product.option %}
\t- {{ option.name }} {{ option.value }}<br/>
{% endfor %}
{% endif %}
{% endfor %}
<br/>
<strong>{{ text_total }}</strong><br/>
<br/>
{% for total in totals %}
{{ total.title }}: {{ total.value }}<br/>
{% endfor %}
<br/>
{% if comment %}
<strong>{{ text_comment }}</strong><br/>
<br/>
{{ comment }}<br/>
<br/>
{% endif %}
{{ store }}<br/>
{{ store_url }}", "catalog/view/template/mail/order_alert.twig", "D:\\wamp64\\www\\pex\\pondexpo\\catalog\\view\\template\\mail\\order_alert.twig");
    }
}
