<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title>{{ title }}</title>
  <base href="{{ base }}"/>
  {% if description %}
    <meta name="description" content="{{ description }}"/>
  {% endif %}
  {% if keywords %}
    <meta name="keywords" content="{{ keywords }}"/>
  {% endif %}

  <!-- Open Graph (OG) Tags -->
  {% for property, content in og_tags %}
    <meta property="{{ property }}" content="{{ content|escape }}"/>
  {% endfor %}

  <!-- Twitter Card Tags -->
  <meta name="twitter:card" content="summary_large_image"/>
  {% if og_tags['og:title'] %}
    <meta name="twitter:title" content="{{ og_tags['og:title']|escape }}"/>
  {% endif %}
  {% if og_tags['og:description'] %}
    <meta name="twitter:description" content="{{ og_tags['og:description']|escape }}"/>
  {% endif %}
  {% if og_tags['og:image'] %}
    <meta name="twitter:image" content="{{ og_tags['og:image']|escape }}"/>
  {% endif %}

  <script src="{{ jquery }}" type="text/javascript"></script>
  <link href="{{ bootstrap }}" type="text/css" rel="stylesheet" media="screen"/>
  <link href="{{ icons }}" rel="stylesheet" type="text/css"/>
  <link href="{{ stylesheet }}" type="text/css" rel="stylesheet"/>
  <script src="catalog/view/javascript/common.js" type="text/javascript"></script>
  {% if icon %}
    <link rel="icon" href="{{ icon }}" type="image/png">
  {% endif %}
  {% for style in styles %}
    <link href="{{ style.href }}" type="text/css" rel="{{ style.rel }}" media="{{ style.media }}"/>
  {% endfor %}
  {% for script in scripts %}
    <script src="{{ script.href }}" type="text/javascript"></script>
  {% endfor %}
  {% for link in links %}
    <link href="{{ link.href }}" rel="{{ link.rel }}"/>
  {% endfor %}
  {% for analytic in analytics %}
    {{ analytic }}
  {% endfor %}
</head>
<body>
<div id="container">
  <div id="alert"></div>
  <div class="topbar">
    <div class="container-fluid">
      <div class="row">
        <div class="col-6 topcontact">
          <ul class="list-inline">
            <li class="list-inline-item"><a href="{{ contact }}"><i class="fa fa-phone"></i> {{ telephone }}</a></li>
            <li class="list-inline-item"><a href="#"><i class="fa-brands fa-facebook-f"></i></a></li>
          </ul>
        </div>
        <div class="col-6">
          <div class="btnoffer">
            <a href="/index.php?route=information/makeoffer" class="btn-offer">Make a No-Risk Offer</a>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <header>
    <div class="container">
      <div class="row">
        <div class="col-md-5">
          <div id="logo">
            {% if logo %}
              <a href="{{ home }}"><img src="{{ logo }}" title="{{ name }}" alt="{{ name }}" class="img-fluid"/></a>
            {% else %}
              <h1><a href="{{ home }}">{{ name }}</a></h1>
            {% endif %}
          </div>
        </div>
        <div class="col-md-7">
          <div class="scroller">
              <marquee behavior="scroll" direction="left" scrollamount="5">
                {# <span class="text">Place to add scrolling text</span> #}
              </marquee>
          </div>
          <div class="account-links-top">
            <nav id="top">
              <div class="container-full">
                <div class="row">
                  <div class="col">
                    <ul class="list-inline">
                      
                      <li class="list-inline-item">
                        <div class="dropdown">
                          <a href="#" class="dropdown-toggle" data-bs-toggle="dropdown"><i class="fa-solid fa-user"></i> <span class="d-none d-lg-inline">{{ text_account }}</span> <i class="fa-solid fa-caret-down"></i></a>
                          <ul class="dropdown-menu dropdown-menu-right">
                            {% if not logged %}
                              <li><a href="{{ register }}" class="dropdown-item">{{ text_register }}</a></li>
                              <li><a href="{{ login }}" class="dropdown-item">{{ text_login }}</a></li>
                            {% else %}
                              <li><a href="{{ account }}" class="dropdown-item">{{ text_account }}</a></li>
                              <li><a href="{{ order }}" class="dropdown-item">{{ text_order }}</a></li>
                              <li><a href="{{ transaction }}" class="dropdown-item">{{ text_transaction }}</a></li>
                              <li><a href="{{ download }}" class="dropdown-item">{{ text_download }}</a></li>
                              <li><a href="{{ logout }}" class="dropdown-item">{{ text_logout }}</a></li>
                            {% endif %}
                          </ul>
                        </div>
                      </li>
                      <li class="list-inline-item"><a href="{{ shopping_cart }}" title="Cart Page"><i class="fa-solid fa-cart-shopping"></i> <span class="d-none d-lg-inline">Cart Page</span></a></li>
                      <li class="list-inline-item"><a href="{{ checkout }}" title="{{ text_checkout }}"><i class="fa-solid fa-share"></i> <span class="d-none d-lg-inline">{{ text_checkout }}</span></a></li>
                      <li class="list-inline-item cartbtn"><div id="cart" class="col-md-3">{{ cart }}</div></li>
                    </ul>
                  </div>
                </div>
              </div>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </header>
  <div class="mainmenu">
    {{ menu }}
  </div>
  <main>

<!-- Search Modal -->
<div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content searchbox">
      {{ search }}
    </div>
  </div>
</div>