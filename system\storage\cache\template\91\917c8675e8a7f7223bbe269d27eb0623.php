<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* extension/opencart/catalog/view/template/module/testimonial.twig */
class __TwigTemplate_c9529a864351007c7a58b3cb45fcf772 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 1
        yield "<div class=\"row\">
  <div class=\"testimonial-module\">
    <h2 class=\"lined-heading\">";
        // line 3
        yield ($context["heading_title"] ?? null);
        yield "</h2>
    ";
        // line 4
        if (($context["testimonials"] ?? null)) {
            // line 5
            yield "      <div id=\"testimonial-carousel\" class=\"carousel slide\">
        <div class=\"carousel-inner\">
          ";
            // line 7
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(($context["testimonials"] ?? null));
            foreach ($context['_seq'] as $context["key"] => $context["testimonial"]) {
                // line 8
                yield "            <div class=\"carousel-item";
                if (($context["key"] == 0)) {
                    yield " active";
                }
                yield "\">
              <div class=\"testimonial-item\">
                <div class=\"row\">
                  ";
                // line 11
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["testimonial"], "image_name", [], "any", false, false, false, 11) != "no_image.png")) {
                    // line 12
                    yield "                    <div class=\"col-md-3 text-center\">
                      <img src=\"";
                    // line 13
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["testimonial"], "image", [], "any", false, false, false, 13);
                    yield "\" alt=\"";
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["testimonial"], "testimonial_by", [], "any", false, false, false, 13);
                    yield "\" class=\"img-fluid rounded-circle mb-3\" style=\"max-width: 120px;\">
                      <h5 class=\"testimonial-author\">";
                    // line 14
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["testimonial"], "testimonial_by", [], "any", false, false, false, 14);
                    yield "</h5>
                      <p class=\"testimonial-designation text-muted\">";
                    // line 15
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["testimonial"], "designation", [], "any", false, false, false, 15);
                    yield "</p>
                    </div>
                    <div class=\"col-md-9\">
                      <div class=\"text-center mb-3\">
                      <h5 class=\"testimonial-author\">";
                    // line 19
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["testimonial"], "testimonial_by", [], "any", false, false, false, 19);
                    yield "</h5>
                      ";
                    // line 21
                    yield "                      </div>
                    </div>
                  ";
                } else {
                    // line 24
                    yield "                    <div class=\"col-md-12\">
                      <div class=\"testimonial-content\">
                        <i class=\"fa-solid fa-quote-left fa-2x text-muted mb-3\"></i>
                        <h3 class=\"testimonial-author\">";
                    // line 27
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["testimonial"], "testimonial_by", [], "any", false, false, false, 27);
                    yield "</h3>
                        <p>";
                    // line 28
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["testimonial"], "testimonial", [], "any", false, false, false, 28);
                    yield "</p>
                        ";
                    // line 30
                    yield "                      </div>
                    </div>
                  ";
                }
                // line 33
                yield "                </div>
              </div>
            </div>
          ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['key'], $context['testimonial'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 37
            yield "        </div>
        ";
            // line 38
            if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), ($context["testimonials"] ?? null)) > 1)) {
                // line 39
                yield "          <button class=\"carousel-control-prev\" type=\"button\" data-bs-target=\"#testimonial-carousel\" data-bs-slide=\"prev\">
            <span class=\"carousel-control-prev-icon\" aria-hidden=\"true\"></span>
            <span class=\"visually-hidden\">Previous</span>
          </button>
          <button class=\"carousel-control-next\" type=\"button\" data-bs-target=\"#testimonial-carousel\" data-bs-slide=\"next\">
            <span class=\"carousel-control-next-icon\" aria-hidden=\"true\"></span>
            <span class=\"visually-hidden\">Next</span>
          </button>
        ";
            }
            // line 48
            yield "      </div>
    ";
        } else {
            // line 50
            yield "      <div class=\"text-center py-4\">
        <p>";
            // line 51
            yield ($context["text_no_testimonials"] ?? null);
            yield "</p>
      </div>
    ";
        }
        // line 54
        yield "  </div>
</div>";
        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "extension/opencart/catalog/view/template/module/testimonial.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  156 => 54,  150 => 51,  147 => 50,  143 => 48,  132 => 39,  130 => 38,  127 => 37,  118 => 33,  113 => 30,  109 => 28,  105 => 27,  100 => 24,  95 => 21,  91 => 19,  84 => 15,  80 => 14,  74 => 13,  71 => 12,  69 => 11,  60 => 8,  56 => 7,  52 => 5,  50 => 4,  46 => 3,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<div class=\"row\">
  <div class=\"testimonial-module\">
    <h2 class=\"lined-heading\">{{ heading_title }}</h2>
    {% if testimonials %}
      <div id=\"testimonial-carousel\" class=\"carousel slide\">
        <div class=\"carousel-inner\">
          {% for key, testimonial in testimonials %}
            <div class=\"carousel-item{% if key == 0 %} active{% endif %}\">
              <div class=\"testimonial-item\">
                <div class=\"row\">
                  {% if testimonial.image_name != 'no_image.png' %}
                    <div class=\"col-md-3 text-center\">
                      <img src=\"{{ testimonial.image }}\" alt=\"{{ testimonial.testimonial_by }}\" class=\"img-fluid rounded-circle mb-3\" style=\"max-width: 120px;\">
                      <h5 class=\"testimonial-author\">{{ testimonial.testimonial_by }}</h5>
                      <p class=\"testimonial-designation text-muted\">{{ testimonial.designation }}</p>
                    </div>
                    <div class=\"col-md-9\">
                      <div class=\"text-center mb-3\">
                      <h5 class=\"testimonial-author\">{{ testimonial.testimonial_by }}</h5>
                      {# <p class=\"testimonial-designation text-muted\">{{ testimonial.designation }}</p> #}
                      </div>
                    </div>
                  {% else %}
                    <div class=\"col-md-12\">
                      <div class=\"testimonial-content\">
                        <i class=\"fa-solid fa-quote-left fa-2x text-muted mb-3\"></i>
                        <h3 class=\"testimonial-author\">{{ testimonial.testimonial_by }}</h3>
                        <p>{{ testimonial.testimonial }}</p>
                        {# <div class=\"testimonial-date text-muted small\">{{ testimonial.date_added }}</div> #}
                      </div>
                    </div>
                  {% endif %}
                </div>
              </div>
            </div>
          {% endfor %}
        </div>
        {% if testimonials|length > 1 %}
          <button class=\"carousel-control-prev\" type=\"button\" data-bs-target=\"#testimonial-carousel\" data-bs-slide=\"prev\">
            <span class=\"carousel-control-prev-icon\" aria-hidden=\"true\"></span>
            <span class=\"visually-hidden\">Previous</span>
          </button>
          <button class=\"carousel-control-next\" type=\"button\" data-bs-target=\"#testimonial-carousel\" data-bs-slide=\"next\">
            <span class=\"carousel-control-next-icon\" aria-hidden=\"true\"></span>
            <span class=\"visually-hidden\">Next</span>
          </button>
        {% endif %}
      </div>
    {% else %}
      <div class=\"text-center py-4\">
        <p>{{ text_no_testimonials }}</p>
      </div>
    {% endif %}
  </div>
</div>", "extension/opencart/catalog/view/template/module/testimonial.twig", "D:\\wamp64\\www\\pex\\pondexpo\\extension\\opencart\\catalog\\view\\template\\module\\testimonial.twig");
    }
}
