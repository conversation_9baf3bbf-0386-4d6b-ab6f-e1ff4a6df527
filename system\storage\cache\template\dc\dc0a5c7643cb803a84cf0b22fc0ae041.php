<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* catalog/view/template/product/thumb.twig */
class __TwigTemplate_857902fcadb97b1e98f62ed0129e5c72 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 1
        yield "<div class=\"product-thumb\">

  ";
        // line 3
        if (($context["special"] ?? null)) {
            // line 4
            yield "\t\t<div class=\"salebadge\" data-bs-toggle=\"tooltip\" title=\"Discount\">
\t\t</div>
\t";
        }
        // line 7
        yield "
  <div class=\"image\"><a href=\"";
        // line 8
        yield ($context["href"] ?? null);
        yield "\"><img src=\"";
        yield ($context["thumb"] ?? null);
        yield "\" alt=\"";
        yield ($context["name"] ?? null);
        yield "\" title=\"";
        yield ($context["name"] ?? null);
        yield "\" class=\"img-fluid\"/></a></div>
  <div class=\"content\">
    <div class=\"description\">
      <h4><a href=\"";
        // line 11
        yield ($context["href"] ?? null);
        yield "\">";
        yield ($context["name"] ?? null);
        yield "</a></h4>
      ";
        // line 12
        if (($context["additional_info1"] ?? null)) {
            // line 13
            yield "\t\t\t\t";
            yield ($context["additional_info1"] ?? null);
            yield "
\t\t\t";
        } else {
            // line 15
            yield "        <p>";
            yield ($context["description"] ?? null);
            yield "</p>
      ";
        }
        // line 17
        yield "      ";
        if (($context["price"] ?? null)) {
            // line 18
            yield "        <div class=\"price\">
          ";
            // line 19
            if ( !($context["special"] ?? null)) {
                // line 20
                yield "            <span class=\"price-new\">";
                yield ($context["price"] ?? null);
                yield "</span>
          ";
            } else {
                // line 22
                yield "            <span class=\"price-old\">";
                yield ($context["price"] ?? null);
                yield "</span> <span class=\"price-new\">";
                yield ($context["special"] ?? null);
                yield "</span>
          ";
            }
            // line 24
            yield "          ";
            if (($context["tax"] ?? null)) {
                // line 25
                yield "            <span class=\"price-tax\">";
                yield ($context["text_tax"] ?? null);
                yield " ";
                yield ($context["tax"] ?? null);
                yield "</span>
          ";
            }
            // line 27
            yield "        </div>
      ";
        }
        // line 29
        yield "      ";
        if ((($context["review_status"] ?? null) && ($context["rating"] ?? null))) {
            // line 30
            yield "        <div class=\"rating\">
          ";
            // line 31
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(range(1, 5));
            foreach ($context['_seq'] as $context["_key"] => $context["i"]) {
                // line 32
                yield "            ";
                if ((($context["rating"] ?? null) < $context["i"])) {
                    // line 33
                    yield "              <span class=\"fa-stack\"><i class=\"fa-regular fa-star fa-stack-1x\"></i></span>
            ";
                } else {
                    // line 35
                    yield "              <span class=\"fa-stack\"><i class=\"fa-solid fa-star fa-stack-1x\"></i><i class=\"fa-regular fa-star fa-stack-1x\"></i></span>
            ";
                }
                // line 37
                yield "          ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['i'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 38
            yield "        </div>
      ";
        }
        // line 40
        yield "    </div>
    <form method=\"post\" data-oc-toggle=\"ajax\" data-oc-load=\"";
        // line 41
        yield ($context["cart"] ?? null);
        yield "\" data-oc-target=\"#cart\">
      <div class=\"button\">
        <button type=\"submit\" formaction=\"";
        // line 43
        yield ($context["wishlist_add"] ?? null);
        yield "\" data-bs-toggle=\"tooltip\" title=\"";
        yield ($context["button_wishlist"] ?? null);
        yield "\" ><i class=\"fa-solid fa-heart\"></i></button>
        <button class=\"long\" type=\"submit\" onclick=\"window.location.href='";
        // line 44
        yield ($context["href"] ?? null);
        yield "'\" data-bs-toggle=\"tooltip\" title=\"";
        yield ($context["button_view"] ?? null);
        yield "\" >";
        yield ($context["button_view"] ?? null);
        yield "</button>
        <button type=\"submit\" formaction=\"";
        // line 45
        yield ($context["compare_add"] ?? null);
        yield "\" data-bs-toggle=\"tooltip\" title=\"";
        yield ($context["button_compare"] ?? null);
        yield "\"><i class=\"fa-solid fa-arrow-right-arrow-left\"></i></button>
      </div>
      <input type=\"hidden\" name=\"product_id\" value=\"";
        // line 47
        yield ($context["product_id"] ?? null);
        yield "\"/> <input type=\"hidden\" name=\"quantity\" value=\"";
        yield ($context["minimum"] ?? null);
        yield "\"/>
    </form>
  </div>
</div>";
        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "catalog/view/template/product/thumb.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  185 => 47,  178 => 45,  170 => 44,  164 => 43,  159 => 41,  156 => 40,  152 => 38,  146 => 37,  142 => 35,  138 => 33,  135 => 32,  131 => 31,  128 => 30,  125 => 29,  121 => 27,  113 => 25,  110 => 24,  102 => 22,  96 => 20,  94 => 19,  91 => 18,  88 => 17,  82 => 15,  76 => 13,  74 => 12,  68 => 11,  56 => 8,  53 => 7,  48 => 4,  46 => 3,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<div class=\"product-thumb\">

  {% if special %}
\t\t<div class=\"salebadge\" data-bs-toggle=\"tooltip\" title=\"Discount\">
\t\t</div>
\t{% endif %}

  <div class=\"image\"><a href=\"{{ href }}\"><img src=\"{{ thumb }}\" alt=\"{{ name }}\" title=\"{{ name }}\" class=\"img-fluid\"/></a></div>
  <div class=\"content\">
    <div class=\"description\">
      <h4><a href=\"{{ href }}\">{{ name }}</a></h4>
      {% if additional_info1 %}
\t\t\t\t{{ additional_info1 }}
\t\t\t{% else %}
        <p>{{ description }}</p>
      {% endif %}
      {% if price %}
        <div class=\"price\">
          {% if not special %}
            <span class=\"price-new\">{{ price }}</span>
          {% else %}
            <span class=\"price-old\">{{ price }}</span> <span class=\"price-new\">{{ special }}</span>
          {% endif %}
          {% if tax %}
            <span class=\"price-tax\">{{ text_tax }} {{ tax }}</span>
          {% endif %}
        </div>
      {% endif %}
      {% if review_status and rating %}
        <div class=\"rating\">
          {% for i in 1..5 %}
            {% if rating < i %}
              <span class=\"fa-stack\"><i class=\"fa-regular fa-star fa-stack-1x\"></i></span>
            {% else %}
              <span class=\"fa-stack\"><i class=\"fa-solid fa-star fa-stack-1x\"></i><i class=\"fa-regular fa-star fa-stack-1x\"></i></span>
            {% endif %}
          {% endfor %}
        </div>
      {% endif %}
    </div>
    <form method=\"post\" data-oc-toggle=\"ajax\" data-oc-load=\"{{ cart }}\" data-oc-target=\"#cart\">
      <div class=\"button\">
        <button type=\"submit\" formaction=\"{{ wishlist_add }}\" data-bs-toggle=\"tooltip\" title=\"{{ button_wishlist }}\" ><i class=\"fa-solid fa-heart\"></i></button>
        <button class=\"long\" type=\"submit\" onclick=\"window.location.href='{{ href }}'\" data-bs-toggle=\"tooltip\" title=\"{{ button_view }}\" >{{ button_view }}</button>
        <button type=\"submit\" formaction=\"{{ compare_add }}\" data-bs-toggle=\"tooltip\" title=\"{{ button_compare }}\"><i class=\"fa-solid fa-arrow-right-arrow-left\"></i></button>
      </div>
      <input type=\"hidden\" name=\"product_id\" value=\"{{ product_id }}\"/> <input type=\"hidden\" name=\"quantity\" value=\"{{ minimum }}\"/>
    </form>
  </div>
</div>", "catalog/view/template/product/thumb.twig", "D:\\wamp64\\www\\pex\\pondexpo\\catalog\\view\\template\\product\\thumb.twig");
    }
}
