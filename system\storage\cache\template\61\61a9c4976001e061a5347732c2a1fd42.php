<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* catalog/view/template/common/footer.twig */
class __TwigTemplate_a9f16eabb1eb76d8d2295fef732a8811 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 1
        yield "</main>
<footer>
\t<section class=\"prefooter\">
\t\t<div class=\"container\">
\t\t\t<div class=\"row\">
\t\t\t\t<div class=\"col-lg-3\">
\t\t\t\t\t<div class=\"card-body\">
\t\t\t\t\t\t<a href=\"index.php?route=product/category&path=60\"><img src=\"/image/catalog/footer-images/pondexpo-footer-pump-image.png\" alt=\"PerformancePro Pumps\"></a>\t
\t\t\t\t\t</div>
\t\t\t\t</div>
\t\t\t\t";
        // line 11
        if ((($context["informations"] ?? null) || ($context["blog"] ?? null))) {
            // line 12
            yield "\t\t\t\t\t<div class=\"col-lg-3\">
\t\t\t\t\t\t<h5>";
            // line 13
            yield ($context["text_information"] ?? null);
            yield "</h5>
\t\t\t\t\t\t<ul class=\"list-unstyled\">
\t\t\t\t\t\t\t";
            // line 15
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(($context["informations"] ?? null));
            foreach ($context['_seq'] as $context["_key"] => $context["information"]) {
                // line 16
                yield "\t\t\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t\t\t<a href=\"";
                // line 17
                yield CoreExtension::getAttribute($this->env, $this->source, $context["information"], "href", [], "any", false, false, false, 17);
                yield "\">";
                yield CoreExtension::getAttribute($this->env, $this->source, $context["information"], "title", [], "any", false, false, false, 17);
                yield "</a>
\t\t\t\t\t\t\t\t</li>
\t\t\t\t\t\t\t";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['information'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 20
            yield "\t\t\t\t\t\t\t";
            if (($context["affiliate"] ?? null)) {
                // line 21
                yield "\t\t\t\t\t\t\t<li><a href=\"";
                yield ($context["blog"] ?? null);
                yield "\">";
                yield ($context["text_blog"] ?? null);
                yield "</a></li>
\t\t\t\t\t\t\t";
            }
            // line 23
            yield "\t\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t\t<a href=\"";
            // line 24
            yield ($context["sitemap"] ?? null);
            yield "\">";
            yield ($context["text_sitemap"] ?? null);
            yield "</a>
\t\t\t\t\t\t\t</li>
\t\t\t\t\t\t</ul>
\t\t\t\t\t</div>
\t\t\t\t";
        }
        // line 29
        yield "\t\t\t\t<div class=\"col-lg-3\">
\t\t\t\t\t<h5>";
        // line 30
        yield ($context["text_service"] ?? null);
        yield "</h5>
\t\t\t\t\t<ul class=\"list-unstyled\">
\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t<a href=\"";
        // line 33
        yield ($context["account"] ?? null);
        yield "\">";
        yield ($context["text_account"] ?? null);
        yield "</a>
\t\t\t\t\t\t</li>
\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t<a href=\"";
        // line 36
        yield ($context["return"] ?? null);
        yield "\">";
        yield ($context["text_return"] ?? null);
        yield "</a>
\t\t\t\t\t\t</li>
\t\t\t\t\t\t";
        // line 38
        if (($context["gdpr"] ?? null)) {
            // line 39
            yield "\t\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t\t<a href=\"";
            // line 40
            yield ($context["gdpr"] ?? null);
            yield "\">";
            yield ($context["text_gdpr"] ?? null);
            yield "</a>
\t\t\t\t\t\t\t</li>
\t\t\t\t\t\t";
        }
        // line 43
        yield "\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t<a href=\"";
        // line 44
        yield ($context["newsletter"] ?? null);
        yield "\">";
        yield ($context["text_newsletter"] ?? null);
        yield "</a>
\t\t\t\t\t\t</li>
\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t<a href=\"";
        // line 47
        yield ($context["contact"] ?? null);
        yield "\">";
        yield ($context["text_contact"] ?? null);
        yield "</a>
\t\t\t\t\t\t</li>
\t\t\t\t\t</ul>
\t\t\t\t</div>
\t\t\t\t<div class=\"col-lg-3\">
\t\t\t\t\t<div class=\"footerlogo\">
            <a href=\"";
        // line 53
        yield ($context["home"] ?? null);
        yield "\"><img src=\"";
        yield ($context["logo"] ?? null);
        yield "\" title=\"";
        yield ($context["name"] ?? null);
        yield "\" alt=\"";
        yield ($context["name"] ?? null);
        yield "\" class=\"img-fluid\"/></a>
          </div>
\t\t\t\t\t<p>";
        // line 55
        yield ($context["address"] ?? null);
        yield "</p>
\t\t\t\t\t<p>
\t\t\t\t\t\t<i class=\"fa fa-envelope\"></i>
\t\t\t\t\t\t";
        // line 58
        yield ($context["email"] ?? null);
        yield "</p>
\t\t\t\t\t<p>
\t\t\t\t\t\t<i class=\"fa fa-phone\"></i>
\t\t\t\t\t\t";
        // line 61
        yield ($context["telephone"] ?? null);
        yield "</p>
\t\t\t\t</div>
\t\t\t</div>
\t\t</div>
\t</section>
\t<section class=\"footermain\">
\t\t<div class=\"container-fluid\">
\t\t\t<div class=\"row\">
\t\t\t\t<div class=\"col-md-4 copyright\">
\t\t\t\t\t<p>&copy;
\t\t\t\t\t\t";
        // line 71
        yield $this->extensions['Twig\Extension\CoreExtension']->formatDate("now", "Y");
        yield " ";
        yield ($context["store"] ?? null);
        yield ", All Rights Reserved.
\t\t\t\t\t</p>
\t\t\t\t</div>
\t\t\t\t<div class=\"col-md-4 text-center footer-icons\">
\t\t\t\t\t<p>
\t\t\t\t\t\tPondexpo - the very best time-proven equipment for your pond or water feature.
\t\t\t\t\t</p>
\t\t\t\t</div>
\t\t\t\t<div class=\"col-md-4 designby\">
\t\t\t\t\t<p>
\t\t\t\t\t\t<a target=\"_blank\" href=\"https://cmswebdev.in\" target=\"_blank\">CMS Web Dev</a>
\t\t\t\t\t</p>
\t\t\t\t</div>
\t\t\t</div>
\t\t</div>
\t</section>
</footer>
</div>
";
        // line 89
        yield ($context["cookie"] ?? null);
        yield "
<script src=\"";
        // line 90
        yield ($context["bootstrap"] ?? null);
        yield "\" type=\"text/javascript\"></script>
";
        // line 91
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(($context["scripts"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["script"]) {
            // line 92
            yield "  <script src=\"";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["script"], "href", [], "any", false, false, false, 92);
            yield "\" type=\"text/javascript\"></script>
";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['script'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 94
        yield "<!-- Go to top -->
<a href=\"#\" class=\"gototop pull-right\">
\t<i class=\"fa-solid fa-chevron-up\"></i>
</a>
<script type=\"text/javascript\">
\t\$(document).ready(function () {
\$(window).scroll(function () {
if (\$(this).scrollTop() > 100) {
\$('.gototop').fadeIn();
} else {
\$('.gototop').fadeOut();
}
});
\$('.gototop').click(function () {
\$(\"html, body\").animate({
scrollTop: 0
}, 600);
return false;
});
});
</script>
<!-- Go to top end -->
<!-- Scrollreveal.js - Animate on Scroll -->
<script src=\"https://unpkg.com/scrollreveal\"></script>
<script>
ScrollReveal({ reset: true });
var slideUp = {
    distance: '100%',
    origin: 'bottom',
    opacity: 0,
    delay: 360,
    duration: 1000,
};
ScrollReveal().reveal('.slide-up', slideUp);

var slideDown = {
    distance: '100%',
    origin: 'top',
    opacity: 0,
    delay: 360,
    duration: 1000,
};
ScrollReveal().reveal('.slide-down', slideDown);

var slideLeft = {
    distance: '50%',
    origin: 'left',
    opacity: 0,
    duration: 1000,
};
ScrollReveal().reveal('.slide-left', slideLeft);

var slideRight = {
    distance: '50%',
    origin: 'right',
    opacity: 0,
    delay: 150,
    duration: 1000,
};
ScrollReveal().reveal('.slide-right', slideRight);

var slideFadeIn = {
    distance: '27%',
    origin: 'bottom',
    opacity: 0,
    delay: 240,
    duration: 1000,
};
ScrollReveal().reveal('.slide-fadein', slideFadeIn);
</script>


</body></html>
";
        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "catalog/view/template/common/footer.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  245 => 94,  236 => 92,  232 => 91,  228 => 90,  224 => 89,  201 => 71,  188 => 61,  182 => 58,  176 => 55,  165 => 53,  154 => 47,  146 => 44,  143 => 43,  135 => 40,  132 => 39,  130 => 38,  123 => 36,  115 => 33,  109 => 30,  106 => 29,  96 => 24,  93 => 23,  85 => 21,  82 => 20,  71 => 17,  68 => 16,  64 => 15,  59 => 13,  56 => 12,  54 => 11,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("</main>
<footer>
\t<section class=\"prefooter\">
\t\t<div class=\"container\">
\t\t\t<div class=\"row\">
\t\t\t\t<div class=\"col-lg-3\">
\t\t\t\t\t<div class=\"card-body\">
\t\t\t\t\t\t<a href=\"index.php?route=product/category&path=60\"><img src=\"/image/catalog/footer-images/pondexpo-footer-pump-image.png\" alt=\"PerformancePro Pumps\"></a>\t
\t\t\t\t\t</div>
\t\t\t\t</div>
\t\t\t\t{% if informations or blog %}
\t\t\t\t\t<div class=\"col-lg-3\">
\t\t\t\t\t\t<h5>{{ text_information }}</h5>
\t\t\t\t\t\t<ul class=\"list-unstyled\">
\t\t\t\t\t\t\t{% for information in informations %}
\t\t\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t\t\t<a href=\"{{ information.href }}\">{{ information.title }}</a>
\t\t\t\t\t\t\t\t</li>
\t\t\t\t\t\t\t{% endfor %}
\t\t\t\t\t\t\t{% if affiliate %}
\t\t\t\t\t\t\t<li><a href=\"{{ blog }}\">{{ text_blog }}</a></li>
\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t\t<a href=\"{{ sitemap }}\">{{ text_sitemap }}</a>
\t\t\t\t\t\t\t</li>
\t\t\t\t\t\t</ul>
\t\t\t\t\t</div>
\t\t\t\t{% endif %}
\t\t\t\t<div class=\"col-lg-3\">
\t\t\t\t\t<h5>{{ text_service }}</h5>
\t\t\t\t\t<ul class=\"list-unstyled\">
\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t<a href=\"{{ account }}\">{{ text_account }}</a>
\t\t\t\t\t\t</li>
\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t<a href=\"{{ return }}\">{{ text_return }}</a>
\t\t\t\t\t\t</li>
\t\t\t\t\t\t{% if gdpr %}
\t\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t\t<a href=\"{{ gdpr }}\">{{ text_gdpr }}</a>
\t\t\t\t\t\t\t</li>
\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t<a href=\"{{ newsletter }}\">{{ text_newsletter }}</a>
\t\t\t\t\t\t</li>
\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t<a href=\"{{ contact }}\">{{ text_contact }}</a>
\t\t\t\t\t\t</li>
\t\t\t\t\t</ul>
\t\t\t\t</div>
\t\t\t\t<div class=\"col-lg-3\">
\t\t\t\t\t<div class=\"footerlogo\">
            <a href=\"{{ home }}\"><img src=\"{{ logo }}\" title=\"{{ name }}\" alt=\"{{ name }}\" class=\"img-fluid\"/></a>
          </div>
\t\t\t\t\t<p>{{ address }}</p>
\t\t\t\t\t<p>
\t\t\t\t\t\t<i class=\"fa fa-envelope\"></i>
\t\t\t\t\t\t{{ email }}</p>
\t\t\t\t\t<p>
\t\t\t\t\t\t<i class=\"fa fa-phone\"></i>
\t\t\t\t\t\t{{ telephone }}</p>
\t\t\t\t</div>
\t\t\t</div>
\t\t</div>
\t</section>
\t<section class=\"footermain\">
\t\t<div class=\"container-fluid\">
\t\t\t<div class=\"row\">
\t\t\t\t<div class=\"col-md-4 copyright\">
\t\t\t\t\t<p>&copy;
\t\t\t\t\t\t{{ \"now\"|date('Y') }} {{ store }}, All Rights Reserved.
\t\t\t\t\t</p>
\t\t\t\t</div>
\t\t\t\t<div class=\"col-md-4 text-center footer-icons\">
\t\t\t\t\t<p>
\t\t\t\t\t\tPondexpo - the very best time-proven equipment for your pond or water feature.
\t\t\t\t\t</p>
\t\t\t\t</div>
\t\t\t\t<div class=\"col-md-4 designby\">
\t\t\t\t\t<p>
\t\t\t\t\t\t<a target=\"_blank\" href=\"https://cmswebdev.in\" target=\"_blank\">CMS Web Dev</a>
\t\t\t\t\t</p>
\t\t\t\t</div>
\t\t\t</div>
\t\t</div>
\t</section>
</footer>
</div>
{{ cookie }}
<script src=\"{{ bootstrap }}\" type=\"text/javascript\"></script>
{% for script in scripts %}
  <script src=\"{{ script.href }}\" type=\"text/javascript\"></script>
{% endfor %}
<!-- Go to top -->
<a href=\"#\" class=\"gototop pull-right\">
\t<i class=\"fa-solid fa-chevron-up\"></i>
</a>
<script type=\"text/javascript\">
\t\$(document).ready(function () {
\$(window).scroll(function () {
if (\$(this).scrollTop() > 100) {
\$('.gototop').fadeIn();
} else {
\$('.gototop').fadeOut();
}
});
\$('.gototop').click(function () {
\$(\"html, body\").animate({
scrollTop: 0
}, 600);
return false;
});
});
</script>
<!-- Go to top end -->
<!-- Scrollreveal.js - Animate on Scroll -->
<script src=\"https://unpkg.com/scrollreveal\"></script>
<script>
ScrollReveal({ reset: true });
var slideUp = {
    distance: '100%',
    origin: 'bottom',
    opacity: 0,
    delay: 360,
    duration: 1000,
};
ScrollReveal().reveal('.slide-up', slideUp);

var slideDown = {
    distance: '100%',
    origin: 'top',
    opacity: 0,
    delay: 360,
    duration: 1000,
};
ScrollReveal().reveal('.slide-down', slideDown);

var slideLeft = {
    distance: '50%',
    origin: 'left',
    opacity: 0,
    duration: 1000,
};
ScrollReveal().reveal('.slide-left', slideLeft);

var slideRight = {
    distance: '50%',
    origin: 'right',
    opacity: 0,
    delay: 150,
    duration: 1000,
};
ScrollReveal().reveal('.slide-right', slideRight);

var slideFadeIn = {
    distance: '27%',
    origin: 'bottom',
    opacity: 0,
    delay: 240,
    duration: 1000,
};
ScrollReveal().reveal('.slide-fadein', slideFadeIn);
</script>


</body></html>
", "catalog/view/template/common/footer.twig", "D:\\wamp64\\www\\pex\\pondexpo\\catalog\\view\\template\\common\\footer.twig");
    }
}
