<?php
namespace Opencart\Catalog\Controller\Common;
/**
 * Class Header
 *
 * Can be called from $this->load->controller('common/header');
 *
 * @package Opencart\Catalog\Controller\Common
 */
class Header extends \Opencart\System\Engine\Controller {
	/**
	 * Index
	 *
	 * @return string
	 */
	public function index(): string {
		// Analytics
		$data['analytics'] = [];

		if (!$this->config->get('config_cookie_id') || (isset($this->request->cookie['policy']) && $this->request->cookie['policy'])) {
			// Extension
			$this->load->model('setting/extension');

			$analytics = $this->model_setting_extension->getExtensionsByType('analytics');

			foreach ($analytics as $analytic) {
				if ($this->config->get('analytics_' . $analytic['code'] . '_status')) {
					$data['analytics'][] = $this->load->controller('extension/' . $analytic['extension'] . '/analytics/' . $analytic['code'], $this->config->get('analytics_' . $analytic['code'] . '_status'));
				}
			}
		}

		$data['lang'] = $this->language->get('code');
		$data['direction'] = $this->language->get('direction');

		$data['title'] = $this->document->getTitle();
		$data['base'] = $this->config->get('config_url');
		$data['description'] = $this->document->getDescription();
		$data['keywords'] = $this->document->getKeywords();

		// Hard coding css, so they can be replaced via the event's system.
		$data['bootstrap'] = 'catalog/view/stylesheet/bootstrap.css';
		$data['icons'] = 'catalog/view/stylesheet/fonts/fontawesome/css/all.min.css';
		$data['stylesheet'] = 'catalog/view/stylesheet/stylesheet.css';

		// Hard coding scripts, so they can be replaced via the event's system.
		$data['jquery'] = 'catalog/view/javascript/jquery/jquery-3.7.1.min.js';

		$data['links'] = $this->document->getLinks();
		$data['styles'] = $this->document->getStyles();
		$data['scripts'] = $this->document->getScripts('header');

		$data['name'] = $this->config->get('config_name');

		// Fav icon
		if (is_file(DIR_IMAGE . $this->config->get('config_icon'))) {
			$data['icon'] = $this->config->get('config_url') . 'image/' . $this->config->get('config_icon');
		} else {
			$data['icon'] = '';
		}

		if (is_file(DIR_IMAGE . $this->config->get('config_logo'))) {
			$data['logo'] = $this->config->get('config_url') . 'image/' . $this->config->get('config_logo');
		} else {
			$data['logo'] = '';
		}

		$this->load->language('common/header');

		// Wishlist
		if ($this->customer->isLogged()) {
			$this->load->model('account/wishlist');

			$data['text_wishlist'] = sprintf($this->language->get('text_wishlist'), $this->model_account_wishlist->getTotalWishlist($this->customer->getId()));
		} else {
			$data['text_wishlist'] = sprintf($this->language->get('text_wishlist'), (isset($this->session->data['wishlist']) ? count($this->session->data['wishlist']) : 0));
		}

		$data['home'] = $this->url->link('common/home', 'language=' . $this->config->get('config_language'));
		$data['wishlist'] = $this->url->link('account/wishlist', 'language=' . $this->config->get('config_language') . (isset($this->session->data['customer_token']) ? '&customer_token=' . $this->session->data['customer_token'] : ''));
		$data['logged'] = $this->customer->isLogged();

		if (!$this->customer->isLogged()) {
			$data['register'] = $this->url->link('account/register', 'language=' . $this->config->get('config_language'));
			$data['login'] = $this->url->link('account/login', 'language=' . $this->config->get('config_language'));
		} else {
			$data['account'] = $this->url->link('account/account', 'language=' . $this->config->get('config_language') . '&customer_token=' . $this->session->data['customer_token']);
			$data['order'] = $this->url->link('account/order', 'language=' . $this->config->get('config_language') . '&customer_token=' . $this->session->data['customer_token']);
			$data['transaction'] = $this->url->link('account/transaction', 'language=' . $this->config->get('config_language') . '&customer_token=' . $this->session->data['customer_token']);
			$data['download'] = $this->url->link('account/download', 'language=' . $this->config->get('config_language') . '&customer_token=' . $this->session->data['customer_token']);
			$data['logout'] = $this->url->link('account/logout', 'language=' . $this->config->get('config_language'));
		}

		$data['shopping_cart'] = $this->url->link('checkout/cart', 'language=' . $this->config->get('config_language'));
		$data['checkout'] = $this->url->link('checkout/checkout', 'language=' . $this->config->get('config_language'));
		$data['contact'] = $this->url->link('information/contact', 'language=' . $this->config->get('config_language'));
		$data['telephone'] = $this->config->get('config_telephone');

		// Generate Open Graph (OG) tags dynamically
		$data['og_tags'] = $this->generateOpenGraphTags();

		$data['language'] = $this->load->controller('common/language');
		$data['currency'] = $this->load->controller('common/currency');
		$data['search'] = $this->load->controller('common/search');
		$data['cart'] = $this->load->controller('common/cart');
		$data['menu'] = $this->load->controller('common/menu');

		return $this->load->view('common/header', $data);
	}

	/**
	 * Generate Open Graph tags dynamically based on current page
	 *
	 * @return array
	 */
	private function generateOpenGraphTags(): array {
		$og_tags = [];

		// Get current route to determine page type
		$route = isset($this->request->get['route']) ? $this->request->get['route'] : 'common/home';

		// Default OG tags for all pages
		$og_tags['og:site_name'] = $this->config->get('config_name');
		$og_tags['og:url'] = $this->getCurrentUrl();
		$og_tags['og:locale'] = $this->getOgLocale();

		// Page-specific OG tags
		if ($route == 'product/product' && isset($this->request->get['product_id'])) {
			$og_tags = array_merge($og_tags, $this->getProductOgTags());
		} elseif ($route == 'product/category' && isset($this->request->get['path'])) {
			$og_tags = array_merge($og_tags, $this->getCategoryOgTags());
		} elseif ($route == 'information/information' && isset($this->request->get['information_id'])) {
			$og_tags = array_merge($og_tags, $this->getInformationOgTags());
		} else {
			$og_tags = array_merge($og_tags, $this->getDefaultOgTags());
		}

		return $og_tags;
	}

	/**
	 * Get current page URL
	 *
	 * @return string
	 */
	private function getCurrentUrl(): string {
		$url = $this->config->get('config_url');

		if (isset($this->request->server['REQUEST_URI'])) {
			$url .= ltrim($this->request->server['REQUEST_URI'], '/');
		}

		return $url;
	}

	/**
	 * Get OG locale based on language
	 *
	 * @return string
	 */
	private function getOgLocale(): string {
		$language_code = $this->language->get('code');

		// Map OpenCart language codes to OG locales
		$locale_map = [
			'en-gb' => 'en_US',
			'en-us' => 'en_US',
			'fr-fr' => 'fr_FR',
			'de-de' => 'de_DE',
			'es-es' => 'es_ES',
			'it-it' => 'it_IT',
			'pt-br' => 'pt_BR',
			'ru-ru' => 'ru_RU',
			'zh-cn' => 'zh_CN',
			'ja-jp' => 'ja_JP'
		];

		return isset($locale_map[$language_code]) ? $locale_map[$language_code] : 'en_US';
	}

	/**
	 * Get Product-specific OG tags
	 *
	 * @return array
	 */
	private function getProductOgTags(): array {
		$og_tags = [];

		$this->load->model('catalog/product');
		$this->load->model('tool/image');

		$product_id = (int)$this->request->get['product_id'];
		$product_info = $this->model_catalog_product->getProduct($product_id);

		if ($product_info) {
			$og_tags['og:type'] = 'product';
			$og_tags['og:title'] = $product_info['meta_title'] ? $product_info['meta_title'] : $product_info['name'];
			$og_tags['og:description'] = $product_info['meta_description'] ? strip_tags($product_info['meta_description']) : strip_tags($product_info['description']);

			// Product image
			if ($product_info['image']) {
				$og_tags['og:image'] = $this->model_tool_image->resize($product_info['image'], 1200, 630);
				$og_tags['og:image:width'] = '1200';
				$og_tags['og:image:height'] = '630';
				$og_tags['og:image:alt'] = $product_info['name'];
			}

			// Product-specific properties
			$og_tags['product:brand'] = $product_info['manufacturer'] ?? $this->config->get('config_name');
			$og_tags['product:availability'] = $product_info['quantity'] > 0 ? 'in stock' : 'out of stock';
			$og_tags['product:condition'] = 'new';

			// Price information
			if ($product_info['price']) {
				$og_tags['product:price:amount'] = $this->currency->format($product_info['price'], $this->session->data['currency'], '', false);
				$og_tags['product:price:currency'] = $this->session->data['currency'];
			}
		}

		return $og_tags;
	}

	/**
	 * Get Category-specific OG tags
	 *
	 * @return array
	 */
	private function getCategoryOgTags(): array {
		$og_tags = [];

		$this->load->model('catalog/category');
		$this->load->model('tool/image');

		$path = explode('_', (string)$this->request->get['path']);
		$category_id = (int)array_pop($path);
		$category_info = $this->model_catalog_category->getCategory($category_id);

		if ($category_info) {
			$og_tags['og:type'] = 'website';
			$og_tags['og:title'] = $category_info['meta_title'] ? $category_info['meta_title'] : $category_info['name'];
			$og_tags['og:description'] = $category_info['meta_description'] ? strip_tags($category_info['meta_description']) : strip_tags($category_info['description']);

			// Category image
			if ($category_info['image']) {
				$og_tags['og:image'] = $this->model_tool_image->resize($category_info['image'], 1200, 630);
				$og_tags['og:image:width'] = '1200';
				$og_tags['og:image:height'] = '630';
				$og_tags['og:image:alt'] = $category_info['name'];
			}
		}

		return $og_tags;
	}

	/**
	 * Get Information page-specific OG tags
	 *
	 * @return array
	 */
	private function getInformationOgTags(): array {
		$og_tags = [];

		$this->load->model('catalog/information');

		$information_id = (int)$this->request->get['information_id'];
		$information_info = $this->model_catalog_information->getInformation($information_id);

		if ($information_info) {
			$og_tags['og:type'] = 'article';
			$og_tags['og:title'] = $information_info['meta_title'] ? $information_info['meta_title'] : $information_info['title'];
			$og_tags['og:description'] = $information_info['meta_description'] ? strip_tags($information_info['meta_description']) : strip_tags($information_info['description']);

			// Use site logo as fallback image for information pages
			if ($this->config->get('config_logo')) {
				$this->load->model('tool/image');
				$og_tags['og:image'] = $this->model_tool_image->resize($this->config->get('config_logo'), 1200, 630);
				$og_tags['og:image:width'] = '1200';
				$og_tags['og:image:height'] = '630';
				$og_tags['og:image:alt'] = $information_info['title'];
			}
		}

		return $og_tags;
	}

	/**
	 * Get Default OG tags for homepage and other pages
	 *
	 * @return array
	 */
	private function getDefaultOgTags(): array {
		$og_tags = [];

		$og_tags['og:type'] = 'website';
		$og_tags['og:title'] = $this->document->getTitle() ? $this->document->getTitle() : $this->config->get('config_name');
		$og_tags['og:description'] = $this->document->getDescription() ? $this->document->getDescription() : $this->config->get('config_meta_description');

		// Use site logo as default image
		if ($this->config->get('config_logo')) {
			$this->load->model('tool/image');
			$og_tags['og:image'] = $this->model_tool_image->resize($this->config->get('config_logo'), 1200, 630);
			$og_tags['og:image:width'] = '1200';
			$og_tags['og:image:height'] = '630';
			$og_tags['og:image:alt'] = $this->config->get('config_name');
		}

		return $og_tags;
	}
}
