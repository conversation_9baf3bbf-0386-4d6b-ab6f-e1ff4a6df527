[{"country_id": "244", "iso_code_2": "AX", "iso_code_3": "ALA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Aaland Islands"}, {"country_id": "1", "iso_code_2": "AF", "iso_code_3": "AFG", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Afghanistan"}, {"country_id": "2", "iso_code_2": "AL", "iso_code_3": "ALB", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Albania"}, {"country_id": "3", "iso_code_2": "DZ", "iso_code_3": "DZA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Algeria"}, {"country_id": "4", "iso_code_2": "AS", "iso_code_3": "ASM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "American Samoa"}, {"country_id": "5", "iso_code_2": "AD", "iso_code_3": "AND", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Andorra"}, {"country_id": "6", "iso_code_2": "AO", "iso_code_3": "AGO", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Angola"}, {"country_id": "7", "iso_code_2": "AI", "iso_code_3": "AIA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "<PERSON><PERSON><PERSON>"}, {"country_id": "8", "iso_code_2": "AQ", "iso_code_3": "ATA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Antarctica"}, {"country_id": "9", "iso_code_2": "AG", "iso_code_3": "ATG", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Antigua and Barbuda"}, {"country_id": "10", "iso_code_2": "AR", "iso_code_3": "ARG", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Argentina"}, {"country_id": "11", "iso_code_2": "AM", "iso_code_3": "ARM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Armenia"}, {"country_id": "12", "iso_code_2": "AW", "iso_code_3": "ABW", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Aruba"}, {"country_id": "252", "iso_code_2": "AC", "iso_code_3": "ASC", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Ascension Island (British)"}, {"country_id": "13", "iso_code_2": "AU", "iso_code_3": "AUS", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Australia"}, {"country_id": "14", "iso_code_2": "AT", "iso_code_3": "AUT", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Austria"}, {"country_id": "15", "iso_code_2": "AZ", "iso_code_3": "AZE", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Azerbaijan"}, {"country_id": "17", "iso_code_2": "BH", "iso_code_3": "BHR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Bahrain"}, {"country_id": "18", "iso_code_2": "BD", "iso_code_3": "BGD", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Bangladesh"}, {"country_id": "19", "iso_code_2": "BB", "iso_code_3": "BRB", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Barbados"}, {"country_id": "20", "iso_code_2": "BY", "iso_code_3": "BLR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Belarus"}, {"country_id": "21", "iso_code_2": "BE", "iso_code_3": "BEL", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Belgium"}, {"country_id": "22", "iso_code_2": "BZ", "iso_code_3": "BLZ", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Belize"}, {"country_id": "23", "iso_code_2": "BJ", "iso_code_3": "BEN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Benin"}, {"country_id": "24", "iso_code_2": "BM", "iso_code_3": "BMU", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Bermuda"}, {"country_id": "25", "iso_code_2": "BT", "iso_code_3": "BTN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Bhutan"}, {"country_id": "26", "iso_code_2": "BO", "iso_code_3": "BOL", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Bolivia"}, {"country_id": "245", "iso_code_2": "BQ", "iso_code_3": "BES", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Bonaire, Sint Eustatius and Saba"}, {"country_id": "27", "iso_code_2": "BA", "iso_code_3": "BIH", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Bosnia and Herzegovina"}, {"country_id": "28", "iso_code_2": "BW", "iso_code_3": "BWA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Botswana"}, {"country_id": "29", "iso_code_2": "BV", "iso_code_3": "BVT", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Bouvet Island"}, {"country_id": "30", "iso_code_2": "BR", "iso_code_3": "BRA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Brazil"}, {"country_id": "31", "iso_code_2": "IO", "iso_code_3": "IOT", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "British Indian Ocean Territory"}, {"country_id": "32", "iso_code_2": "BN", "iso_code_3": "BRN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Brunei"}, {"country_id": "33", "iso_code_2": "BG", "iso_code_3": "BGR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Bulgaria"}, {"country_id": "34", "iso_code_2": "BF", "iso_code_3": "BFA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Burkina Faso"}, {"country_id": "35", "iso_code_2": "BI", "iso_code_3": "BDI", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Burundi"}, {"country_id": "36", "iso_code_2": "KH", "iso_code_3": "KHM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Cambodia"}, {"country_id": "37", "iso_code_2": "CM", "iso_code_3": "CMR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Cameroon"}, {"country_id": "38", "iso_code_2": "CA", "iso_code_3": "CAN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Canada"}, {"country_id": "251", "iso_code_2": "IC", "iso_code_3": "ICA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Canary Islands"}, {"country_id": "39", "iso_code_2": "CV", "iso_code_3": "CPV", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Cape Verde"}, {"country_id": "40", "iso_code_2": "KY", "iso_code_3": "CYM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Cayman Islands"}, {"country_id": "41", "iso_code_2": "CF", "iso_code_3": "CAF", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Central African Republic"}, {"country_id": "42", "iso_code_2": "TD", "iso_code_3": "TCD", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Chad"}, {"country_id": "43", "iso_code_2": "CL", "iso_code_3": "CHL", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Chile"}, {"country_id": "44", "iso_code_2": "CN", "iso_code_3": "CHN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "China"}, {"country_id": "45", "iso_code_2": "CX", "iso_code_3": "CXR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Christmas Island"}, {"country_id": "46", "iso_code_2": "CC", "iso_code_3": "CCK", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Cocos (Keeling) Islands"}, {"country_id": "47", "iso_code_2": "CO", "iso_code_3": "COL", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Colombia"}, {"country_id": "48", "iso_code_2": "KM", "iso_code_3": "COM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Comoros"}, {"country_id": "49", "iso_code_2": "CG", "iso_code_3": "COG", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Congo"}, {"country_id": "50", "iso_code_2": "CK", "iso_code_3": "COK", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Cook Islands"}, {"country_id": "51", "iso_code_2": "CR", "iso_code_3": "CRI", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Costa Rica"}, {"country_id": "52", "iso_code_2": "CI", "iso_code_3": "CIV", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Cote D'Ivoire"}, {"country_id": "53", "iso_code_2": "HR", "iso_code_3": "HRV", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Croatia"}, {"country_id": "246", "iso_code_2": "CW", "iso_code_3": "CUW", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Curacao"}, {"country_id": "55", "iso_code_2": "CY", "iso_code_3": "CYP", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Cyprus"}, {"country_id": "56", "iso_code_2": "CZ", "iso_code_3": "CZE", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Czech Republic"}, {"country_id": "237", "iso_code_2": "CD", "iso_code_3": "COD", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Democratic Republic of the Congo"}, {"country_id": "57", "iso_code_2": "DK", "iso_code_3": "DNK", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Denmark"}, {"country_id": "58", "iso_code_2": "DJ", "iso_code_3": "DJI", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Djibouti"}, {"country_id": "59", "iso_code_2": "DM", "iso_code_3": "DMA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Dominica"}, {"country_id": "60", "iso_code_2": "DO", "iso_code_3": "DOM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Dominican Republic"}, {"country_id": "62", "iso_code_2": "EC", "iso_code_3": "ECU", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Ecuador"}, {"country_id": "63", "iso_code_2": "EG", "iso_code_3": "EGY", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Egypt"}, {"country_id": "64", "iso_code_2": "SV", "iso_code_3": "SLV", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "El Salvador"}, {"country_id": "65", "iso_code_2": "GQ", "iso_code_3": "GNQ", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Equatorial Guinea"}, {"country_id": "66", "iso_code_2": "ER", "iso_code_3": "ERI", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Eritrea"}, {"country_id": "67", "iso_code_2": "EE", "iso_code_3": "EST", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Estonia"}, {"country_id": "202", "iso_code_2": "SZ", "iso_code_3": "SWZ", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"country_id": "68", "iso_code_2": "ET", "iso_code_3": "ETH", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Ethiopia"}, {"country_id": "69", "iso_code_2": "FK", "iso_code_3": "FLK", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Falkland Islands (Malvinas)"}, {"country_id": "70", "iso_code_2": "FO", "iso_code_3": "FRO", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Faroe Islands"}, {"country_id": "71", "iso_code_2": "FJ", "iso_code_3": "FJI", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Fiji"}, {"country_id": "72", "iso_code_2": "FI", "iso_code_3": "FIN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Finland"}, {"country_id": "74", "iso_code_2": "FR", "iso_code_3": "FRA", "address_format_id": "1", "postcode_required": "1", "status": "1", "language_id": "1", "name": "France, Metropolitan"}, {"country_id": "75", "iso_code_2": "GF", "iso_code_3": "GUF", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "French Guiana"}, {"country_id": "76", "iso_code_2": "PF", "iso_code_3": "PYF", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "French Polynesia"}, {"country_id": "77", "iso_code_2": "TF", "iso_code_3": "ATF", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "French Southern Territories"}, {"country_id": "78", "iso_code_2": "GA", "iso_code_3": "GAB", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Gabon"}, {"country_id": "80", "iso_code_2": "GE", "iso_code_3": "GEO", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Georgia"}, {"country_id": "81", "iso_code_2": "DE", "iso_code_3": "DEU", "address_format_id": "1", "postcode_required": "1", "status": "1", "language_id": "1", "name": "Germany"}, {"country_id": "82", "iso_code_2": "GH", "iso_code_3": "GHA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Ghana"}, {"country_id": "83", "iso_code_2": "GI", "iso_code_3": "GIB", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Gibraltar"}, {"country_id": "84", "iso_code_2": "GR", "iso_code_3": "GRC", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Greece"}, {"country_id": "85", "iso_code_2": "GL", "iso_code_3": "GRL", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Greenland"}, {"country_id": "86", "iso_code_2": "GD", "iso_code_3": "GRD", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Grenada"}, {"country_id": "87", "iso_code_2": "GP", "iso_code_3": "GLP", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Guadeloupe"}, {"country_id": "88", "iso_code_2": "GU", "iso_code_3": "GUM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Guam"}, {"country_id": "89", "iso_code_2": "GT", "iso_code_3": "GTM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Guatemala"}, {"country_id": "256", "iso_code_2": "GG", "iso_code_3": "GGY", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Guernsey"}, {"country_id": "90", "iso_code_2": "GN", "iso_code_3": "GIN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Guinea"}, {"country_id": "91", "iso_code_2": "GW", "iso_code_3": "GNB", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Guinea-Bissau"}, {"country_id": "92", "iso_code_2": "GY", "iso_code_3": "GUY", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Guyana"}, {"country_id": "93", "iso_code_2": "HT", "iso_code_3": "HTI", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Haiti"}, {"country_id": "94", "iso_code_2": "HM", "iso_code_3": "HMD", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Heard and Mc Donald Islands"}, {"country_id": "95", "iso_code_2": "HN", "iso_code_3": "HND", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Honduras"}, {"country_id": "96", "iso_code_2": "HK", "iso_code_3": "HKG", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Hong Kong"}, {"country_id": "97", "iso_code_2": "HU", "iso_code_3": "HUN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Hungary"}, {"country_id": "98", "iso_code_2": "IS", "iso_code_3": "ISL", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Iceland"}, {"country_id": "100", "iso_code_2": "ID", "iso_code_3": "IDN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Indonesia"}, {"country_id": "102", "iso_code_2": "IQ", "iso_code_3": "IRQ", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Iraq"}, {"country_id": "103", "iso_code_2": "IE", "iso_code_3": "IRL", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Ireland"}, {"country_id": "254", "iso_code_2": "IM", "iso_code_3": "IMN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Isle of Man"}, {"country_id": "104", "iso_code_2": "IL", "iso_code_3": "ISR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Israel"}, {"country_id": "105", "iso_code_2": "IT", "iso_code_3": "ITA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Italy"}, {"country_id": "106", "iso_code_2": "JM", "iso_code_3": "JAM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Jamaica"}, {"country_id": "107", "iso_code_2": "JP", "iso_code_3": "JPN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Japan"}, {"country_id": "257", "iso_code_2": "JE", "iso_code_3": "JEY", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Jersey"}, {"country_id": "108", "iso_code_2": "JO", "iso_code_3": "JOR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Jordan"}, {"country_id": "109", "iso_code_2": "KZ", "iso_code_3": "KAZ", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Kazakhstan"}, {"country_id": "110", "iso_code_2": "KE", "iso_code_3": "KEN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Kenya"}, {"country_id": "111", "iso_code_2": "KI", "iso_code_3": "KIR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Kiribati"}, {"country_id": "253", "iso_code_2": "XK", "iso_code_3": "UNK", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Kosovo, Republic of"}, {"country_id": "114", "iso_code_2": "KW", "iso_code_3": "KWT", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Kuwait"}, {"country_id": "115", "iso_code_2": "KG", "iso_code_3": "KGZ", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Kyrgyzstan"}, {"country_id": "116", "iso_code_2": "LA", "iso_code_3": "LAO", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Lao People's Democratic Republic (the)"}, {"country_id": "117", "iso_code_2": "LV", "iso_code_3": "LVA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Latvia"}, {"country_id": "118", "iso_code_2": "LB", "iso_code_3": "LBN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Lebanon"}, {"country_id": "119", "iso_code_2": "LS", "iso_code_3": "LSO", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Lesotho"}, {"country_id": "120", "iso_code_2": "LR", "iso_code_3": "LBR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Liberia"}, {"country_id": "121", "iso_code_2": "LY", "iso_code_3": "LBY", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Libya"}, {"country_id": "122", "iso_code_2": "LI", "iso_code_3": "LIE", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Liechtenstein"}, {"country_id": "123", "iso_code_2": "LT", "iso_code_3": "LTU", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Lithuania"}, {"country_id": "124", "iso_code_2": "LU", "iso_code_3": "LUX", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Luxembourg"}, {"country_id": "125", "iso_code_2": "MO", "iso_code_3": "MAC", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Macau"}, {"country_id": "127", "iso_code_2": "MG", "iso_code_3": "MDG", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Madagascar"}, {"country_id": "128", "iso_code_2": "MW", "iso_code_3": "MWI", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Malawi"}, {"country_id": "129", "iso_code_2": "MY", "iso_code_3": "MYS", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Malaysia"}, {"country_id": "130", "iso_code_2": "MV", "iso_code_3": "MDV", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Maldives"}, {"country_id": "131", "iso_code_2": "ML", "iso_code_3": "MLI", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Mali"}, {"country_id": "132", "iso_code_2": "MT", "iso_code_3": "MLT", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Malta"}, {"country_id": "133", "iso_code_2": "MH", "iso_code_3": "MHL", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Marshall Islands"}, {"country_id": "134", "iso_code_2": "MQ", "iso_code_3": "MTQ", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Martinique"}, {"country_id": "135", "iso_code_2": "MR", "iso_code_3": "MRT", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Mauritania"}, {"country_id": "136", "iso_code_2": "MU", "iso_code_3": "MUS", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Mauritius"}, {"country_id": "137", "iso_code_2": "YT", "iso_code_3": "MYT", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Mayotte"}, {"country_id": "138", "iso_code_2": "MX", "iso_code_3": "MEX", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Mexico"}, {"country_id": "139", "iso_code_2": "FM", "iso_code_3": "FSM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Micronesia, Federated States of"}, {"country_id": "140", "iso_code_2": "MD", "iso_code_3": "MDA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Moldova, Republic of"}, {"country_id": "141", "iso_code_2": "MC", "iso_code_3": "MCO", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Monaco"}, {"country_id": "142", "iso_code_2": "MN", "iso_code_3": "MNG", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Mongolia"}, {"country_id": "242", "iso_code_2": "ME", "iso_code_3": "MNE", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Montenegro"}, {"country_id": "143", "iso_code_2": "MS", "iso_code_3": "MSR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Montserrat"}, {"country_id": "144", "iso_code_2": "MA", "iso_code_3": "MAR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Morocco"}, {"country_id": "145", "iso_code_2": "MZ", "iso_code_3": "MOZ", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Mozambique"}, {"country_id": "146", "iso_code_2": "MM", "iso_code_3": "MMR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Myanmar"}, {"country_id": "147", "iso_code_2": "NA", "iso_code_3": "NAM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Namibia"}, {"country_id": "148", "iso_code_2": "NR", "iso_code_3": "NRU", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Nauru"}, {"country_id": "149", "iso_code_2": "NP", "iso_code_3": "NPL", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Nepal"}, {"country_id": "150", "iso_code_2": "NL", "iso_code_3": "NLD", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Netherlands"}, {"country_id": "151", "iso_code_2": "AN", "iso_code_3": "ANT", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Netherlands Antilles"}, {"country_id": "152", "iso_code_2": "NC", "iso_code_3": "NCL", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "New Caledonia"}, {"country_id": "153", "iso_code_2": "NZ", "iso_code_3": "NZL", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "New Zealand"}, {"country_id": "154", "iso_code_2": "NI", "iso_code_3": "NIC", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Nicaragua"}, {"country_id": "155", "iso_code_2": "NE", "iso_code_3": "NER", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Niger"}, {"country_id": "156", "iso_code_2": "NG", "iso_code_3": "NGA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Nigeria"}, {"country_id": "157", "iso_code_2": "NU", "iso_code_3": "NIU", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Niue"}, {"country_id": "158", "iso_code_2": "NF", "iso_code_3": "NFK", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Norfolk Island"}, {"country_id": "126", "iso_code_2": "MK", "iso_code_3": "MKD", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "North Macedonia"}, {"country_id": "159", "iso_code_2": "MP", "iso_code_3": "MNP", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Northern Mariana Islands"}, {"country_id": "160", "iso_code_2": "NO", "iso_code_3": "NOR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Norway"}, {"country_id": "247", "iso_code_2": "PS", "iso_code_3": "PSE", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Occupied Palestinian Territory"}, {"country_id": "161", "iso_code_2": "OM", "iso_code_3": "OMN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Oman"}, {"country_id": "162", "iso_code_2": "PK", "iso_code_3": "PAK", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Pakistan"}, {"country_id": "163", "iso_code_2": "PW", "iso_code_3": "PLW", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "<PERSON><PERSON>"}, {"country_id": "164", "iso_code_2": "PA", "iso_code_3": "PAN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Panama"}, {"country_id": "165", "iso_code_2": "PG", "iso_code_3": "PNG", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Papua New Guinea"}, {"country_id": "166", "iso_code_2": "PY", "iso_code_3": "PRY", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Paraguay"}, {"country_id": "167", "iso_code_2": "PE", "iso_code_3": "PER", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Peru"}, {"country_id": "168", "iso_code_2": "PH", "iso_code_3": "PHL", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Philippines"}, {"country_id": "169", "iso_code_2": "PN", "iso_code_3": "PCN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Pitcairn"}, {"country_id": "170", "iso_code_2": "PL", "iso_code_3": "POL", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Poland"}, {"country_id": "171", "iso_code_2": "PT", "iso_code_3": "PRT", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Portugal"}, {"country_id": "172", "iso_code_2": "PR", "iso_code_3": "PRI", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Puerto Rico"}, {"country_id": "173", "iso_code_2": "QA", "iso_code_3": "QAT", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Qatar"}, {"country_id": "215", "iso_code_2": "TR", "iso_code_3": "TUR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Republic of Türkiye"}, {"country_id": "174", "iso_code_2": "RE", "iso_code_3": "REU", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Reunion"}, {"country_id": "175", "iso_code_2": "RO", "iso_code_3": "ROM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Romania"}, {"country_id": "176", "iso_code_2": "RU", "iso_code_3": "RUS", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Russian Federation (the)"}, {"country_id": "177", "iso_code_2": "RW", "iso_code_3": "RWA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Rwanda"}, {"country_id": "178", "iso_code_2": "KN", "iso_code_3": "KNA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Saint Kitts and Nevis"}, {"country_id": "179", "iso_code_2": "LC", "iso_code_3": "LCA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Saint Lucia"}, {"country_id": "180", "iso_code_2": "VC", "iso_code_3": "VCT", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Saint Vincent and the Grenadines"}, {"country_id": "181", "iso_code_2": "WS", "iso_code_3": "WSM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Samoa"}, {"country_id": "182", "iso_code_2": "SM", "iso_code_3": "SMR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "San Marino"}, {"country_id": "183", "iso_code_2": "ST", "iso_code_3": "STP", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Sao Tome and Principe"}, {"country_id": "184", "iso_code_2": "SA", "iso_code_3": "SAU", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Saudi Arabia"}, {"country_id": "185", "iso_code_2": "SN", "iso_code_3": "SEN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Senegal"}, {"country_id": "243", "iso_code_2": "RS", "iso_code_3": "SRB", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Serbia"}, {"country_id": "186", "iso_code_2": "SC", "iso_code_3": "SYC", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Seychelles"}, {"country_id": "187", "iso_code_2": "SL", "iso_code_3": "SLE", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Sierra Leone"}, {"country_id": "188", "iso_code_2": "SG", "iso_code_3": "SGP", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Singapore"}, {"country_id": "189", "iso_code_2": "SK", "iso_code_3": "SVK", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Slovak Republic"}, {"country_id": "190", "iso_code_2": "SI", "iso_code_3": "SVN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Slovenia"}, {"country_id": "191", "iso_code_2": "SB", "iso_code_3": "SLB", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Solomon Islands"}, {"country_id": "192", "iso_code_2": "SO", "iso_code_3": "SOM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Somalia"}, {"country_id": "193", "iso_code_2": "ZA", "iso_code_3": "ZAF", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "South Africa"}, {"country_id": "194", "iso_code_2": "GS", "iso_code_3": "SGS", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "South Georgia &amp; South Sandwich Islands"}, {"country_id": "113", "iso_code_2": "KR", "iso_code_3": "KOR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "South Korea"}, {"country_id": "248", "iso_code_2": "SS", "iso_code_3": "SSD", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "South Sudan"}, {"country_id": "195", "iso_code_2": "ES", "iso_code_3": "ESP", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Spain"}, {"country_id": "196", "iso_code_2": "LK", "iso_code_3": "LKA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Sri Lanka"}, {"country_id": "249", "iso_code_2": "BL", "iso_code_3": "BLM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "St. <PERSON>"}, {"country_id": "197", "iso_code_2": "SH", "iso_code_3": "SHN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "St. Helena"}, {"country_id": "250", "iso_code_2": "MF", "iso_code_3": "MAF", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "<PERSON><PERSON> (French part)"}, {"country_id": "198", "iso_code_2": "PM", "iso_code_3": "SPM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "St. Pierre and Miquelon"}, {"country_id": "199", "iso_code_2": "SD", "iso_code_3": "SDN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Sudan"}, {"country_id": "200", "iso_code_2": "SR", "iso_code_3": "SUR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Suriname"}, {"country_id": "201", "iso_code_2": "SJ", "iso_code_3": "SJM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Svalbard and Jan Mayen Islands"}, {"country_id": "203", "iso_code_2": "SE", "iso_code_3": "SWE", "address_format_id": "1", "postcode_required": "1", "status": "1", "language_id": "1", "name": "Sweden"}, {"country_id": "204", "iso_code_2": "CH", "iso_code_3": "CHE", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Switzerland"}, {"country_id": "206", "iso_code_2": "TW", "iso_code_3": "TWN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Taiwan"}, {"country_id": "207", "iso_code_2": "TJ", "iso_code_3": "TJK", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Tajikistan"}, {"country_id": "208", "iso_code_2": "TZ", "iso_code_3": "TZA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Tanzania, United Republic of"}, {"country_id": "209", "iso_code_2": "TH", "iso_code_3": "THA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Thailand"}, {"country_id": "16", "iso_code_2": "BS", "iso_code_3": "BHS", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "The Bahamas"}, {"country_id": "79", "iso_code_2": "GM", "iso_code_3": "GMB", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "The Gambia"}, {"country_id": "61", "iso_code_2": "TL", "iso_code_3": "TLS", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Timor-Leste"}, {"country_id": "210", "iso_code_2": "TG", "iso_code_3": "TGO", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Togo"}, {"country_id": "211", "iso_code_2": "TK", "iso_code_3": "TKL", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Tokelau"}, {"country_id": "212", "iso_code_2": "TO", "iso_code_3": "TON", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Tonga"}, {"country_id": "213", "iso_code_2": "TT", "iso_code_3": "TTO", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Trinidad and Tobago"}, {"country_id": "255", "iso_code_2": "TA", "iso_code_3": "SHN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "<PERSON>"}, {"country_id": "214", "iso_code_2": "TN", "iso_code_3": "TUN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Tunisia"}, {"country_id": "216", "iso_code_2": "TM", "iso_code_3": "TKM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Turkmenistan"}, {"country_id": "217", "iso_code_2": "TC", "iso_code_3": "TCA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Turks and Caicos Islands"}, {"country_id": "218", "iso_code_2": "TV", "iso_code_3": "TUV", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Tuvalu"}, {"country_id": "219", "iso_code_2": "UG", "iso_code_3": "UGA", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Uganda"}, {"country_id": "220", "iso_code_2": "UA", "iso_code_3": "UKR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Ukraine"}, {"country_id": "221", "iso_code_2": "AE", "iso_code_3": "ARE", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "United Arab Emirates"}, {"country_id": "222", "iso_code_2": "GB", "iso_code_3": "GBR", "address_format_id": "1", "postcode_required": "1", "status": "1", "language_id": "1", "name": "United Kingdom"}, {"country_id": "223", "iso_code_2": "US", "iso_code_3": "USA", "address_format_id": "1", "postcode_required": "1", "status": "1", "language_id": "1", "name": "United States"}, {"country_id": "224", "iso_code_2": "UM", "iso_code_3": "UMI", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "United States Minor Outlying Islands"}, {"country_id": "225", "iso_code_2": "UY", "iso_code_3": "URY", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Uruguay"}, {"country_id": "226", "iso_code_2": "UZ", "iso_code_3": "UZB", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Uzbekistan"}, {"country_id": "227", "iso_code_2": "VU", "iso_code_3": "VUT", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Vanuatu"}, {"country_id": "228", "iso_code_2": "VA", "iso_code_3": "VAT", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Vatican City State (Holy See)"}, {"country_id": "229", "iso_code_2": "VE", "iso_code_3": "VEN", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Venezuela"}, {"country_id": "230", "iso_code_2": "VN", "iso_code_3": "VNM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Viet Nam"}, {"country_id": "231", "iso_code_2": "VG", "iso_code_3": "VGB", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Virgin Islands (British)"}, {"country_id": "232", "iso_code_2": "VI", "iso_code_3": "VIR", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Virgin Islands (U.S.)"}, {"country_id": "233", "iso_code_2": "WF", "iso_code_3": "WLF", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Wallis and Futuna Islands"}, {"country_id": "234", "iso_code_2": "EH", "iso_code_3": "ESH", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Western Sahara"}, {"country_id": "235", "iso_code_2": "YE", "iso_code_3": "YEM", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Yemen"}, {"country_id": "238", "iso_code_2": "ZM", "iso_code_3": "ZMB", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Zambia"}, {"country_id": "239", "iso_code_2": "ZW", "iso_code_3": "ZWE", "address_format_id": "1", "postcode_required": "0", "status": "1", "language_id": "1", "name": "Zimbabwe"}]