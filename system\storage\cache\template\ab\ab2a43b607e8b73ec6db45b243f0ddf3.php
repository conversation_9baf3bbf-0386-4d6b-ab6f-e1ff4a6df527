<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* extension/opencart/catalog/view/template/module/html.twig */
class __TwigTemplate_84a19d29cfeb07ba4e526d2fb6071921 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 1
        yield "<div>
\t";
        // line 2
        if (($context["heading_title"] ?? null)) {
            // line 3
            yield "\t\t<h2>";
            yield ($context["heading_title"] ?? null);
            yield "</h2>
\t";
        }
        // line 5
        yield "\t";
        yield ($context["html"] ?? null);
        yield "
</div>
";
        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "extension/opencart/catalog/view/template/module/html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  53 => 5,  47 => 3,  45 => 2,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<div>
\t{% if heading_title %}
\t\t<h2>{{ heading_title }}</h2>
\t{% endif %}
\t{{ html }}
</div>
", "extension/opencart/catalog/view/template/module/html.twig", "D:\\wamp64\\www\\pex\\pondexpo\\extension\\opencart\\catalog\\view\\template\\module\\html.twig");
    }
}
