<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* catalog/view/template/common/header.twig */
class __TwigTemplate_7c8d39e7c1c0f33109d247a71b99b99a extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 1
        yield "<!DOCTYPE html>
<html dir=\"";
        // line 2
        yield ($context["direction"] ?? null);
        yield "\" lang=\"";
        yield ($context["lang"] ?? null);
        yield "\">
<head>
  <meta charset=\"UTF-8\"/>
  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">
  <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">
  <title>";
        // line 7
        yield ($context["title"] ?? null);
        yield "</title>
  <base href=\"";
        // line 8
        yield ($context["base"] ?? null);
        yield "\"/>
  ";
        // line 9
        if (($context["description"] ?? null)) {
            // line 10
            yield "    <meta name=\"description\" content=\"";
            yield ($context["description"] ?? null);
            yield "\"/>
  ";
        }
        // line 12
        yield "  ";
        if (($context["keywords"] ?? null)) {
            // line 13
            yield "    <meta name=\"keywords\" content=\"";
            yield ($context["keywords"] ?? null);
            yield "\"/>
  ";
        }
        // line 15
        yield "
  <!-- Open Graph (OG) Tags -->
  ";
        // line 17
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(($context["og_tags"] ?? null));
        foreach ($context['_seq'] as $context["property"] => $context["content"]) {
            // line 18
            yield "    <meta property=\"";
            yield $context["property"];
            yield "\" content=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["content"]);
            yield "\"/>
  ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['property'], $context['content'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 20
        yield "
  <!-- Twitter Card Tags -->
  <meta name=\"twitter:card\" content=\"summary_large_image\"/>
  ";
        // line 23
        if ((($_v0 = ($context["og_tags"] ?? null)) && is_array($_v0) || $_v0 instanceof ArrayAccess ? ($_v0["og:title"] ?? null) : null)) {
            // line 24
            yield "    <meta name=\"twitter:title\" content=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((($_v1 = ($context["og_tags"] ?? null)) && is_array($_v1) || $_v1 instanceof ArrayAccess ? ($_v1["og:title"] ?? null) : null));
            yield "\"/>
  ";
        }
        // line 26
        yield "  ";
        if ((($_v2 = ($context["og_tags"] ?? null)) && is_array($_v2) || $_v2 instanceof ArrayAccess ? ($_v2["og:description"] ?? null) : null)) {
            // line 27
            yield "    <meta name=\"twitter:description\" content=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((($_v3 = ($context["og_tags"] ?? null)) && is_array($_v3) || $_v3 instanceof ArrayAccess ? ($_v3["og:description"] ?? null) : null));
            yield "\"/>
  ";
        }
        // line 29
        yield "  ";
        if ((($_v4 = ($context["og_tags"] ?? null)) && is_array($_v4) || $_v4 instanceof ArrayAccess ? ($_v4["og:image"] ?? null) : null)) {
            // line 30
            yield "    <meta name=\"twitter:image\" content=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((($_v5 = ($context["og_tags"] ?? null)) && is_array($_v5) || $_v5 instanceof ArrayAccess ? ($_v5["og:image"] ?? null) : null));
            yield "\"/>
  ";
        }
        // line 32
        yield "
  <script src=\"";
        // line 33
        yield ($context["jquery"] ?? null);
        yield "\" type=\"text/javascript\"></script>
  <link href=\"";
        // line 34
        yield ($context["bootstrap"] ?? null);
        yield "\" type=\"text/css\" rel=\"stylesheet\" media=\"screen\"/>
  <link href=\"";
        // line 35
        yield ($context["icons"] ?? null);
        yield "\" rel=\"stylesheet\" type=\"text/css\"/>
  <link href=\"";
        // line 36
        yield ($context["stylesheet"] ?? null);
        yield "\" type=\"text/css\" rel=\"stylesheet\"/>
  <script src=\"catalog/view/javascript/common.js\" type=\"text/javascript\"></script>
  ";
        // line 38
        if (($context["icon"] ?? null)) {
            // line 39
            yield "    <link rel=\"icon\" href=\"";
            yield ($context["icon"] ?? null);
            yield "\" type=\"image/png\">
  ";
        }
        // line 41
        yield "  ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(($context["styles"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["style"]) {
            // line 42
            yield "    <link href=\"";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["style"], "href", [], "any", false, false, false, 42);
            yield "\" type=\"text/css\" rel=\"";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["style"], "rel", [], "any", false, false, false, 42);
            yield "\" media=\"";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["style"], "media", [], "any", false, false, false, 42);
            yield "\"/>
  ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['style'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 44
        yield "  ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(($context["scripts"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["script"]) {
            // line 45
            yield "    <script src=\"";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["script"], "href", [], "any", false, false, false, 45);
            yield "\" type=\"text/javascript\"></script>
  ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['script'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 47
        yield "  ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(($context["links"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["link"]) {
            // line 48
            yield "    <link href=\"";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["link"], "href", [], "any", false, false, false, 48);
            yield "\" rel=\"";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["link"], "rel", [], "any", false, false, false, 48);
            yield "\"/>
  ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['link'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 50
        yield "  ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(($context["analytics"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["analytic"]) {
            // line 51
            yield "    ";
            yield $context["analytic"];
            yield "
  ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['analytic'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 53
        yield "  <!-- Google tag (gtag.js) -->
  <script async src=\" https://www.googletagmanager.com/gtag/js?id=G-94992TXH1J\"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-94992TXH1J');
 </script>
</head>
<body>
<div id=\"container\">
  <div id=\"alert\"></div>
  <div class=\"topbar\">
    <div class=\"container-fluid\">
      <div class=\"row\">
        <div class=\"col-6 topcontact\">
          <ul class=\"list-inline\">
            <li class=\"list-inline-item\"><a href=\"";
        // line 71
        yield ($context["contact"] ?? null);
        yield "\"><i class=\"fa fa-phone\"></i> ";
        yield ($context["telephone"] ?? null);
        yield "</a></li>
            <li class=\"list-inline-item\"><a href=\"#\"><i class=\"fa-brands fa-facebook-f\"></i></a></li>
          </ul>
        </div>
        <div class=\"col-6\">
          <div class=\"btnoffer\">
            <a href=\"/index.php?route=information/makeoffer\" class=\"btn-offer\">Make a No-Risk Offer</a>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <header>
    <div class=\"container\">
      <div class=\"row\">
        <div class=\"col-md-5\">
          <div id=\"logo\">
            ";
        // line 89
        if (($context["logo"] ?? null)) {
            // line 90
            yield "              <a href=\"";
            yield ($context["home"] ?? null);
            yield "\"><img src=\"";
            yield ($context["logo"] ?? null);
            yield "\" title=\"";
            yield ($context["name"] ?? null);
            yield "\" alt=\"";
            yield ($context["name"] ?? null);
            yield "\" class=\"img-fluid\"/></a>
            ";
        } else {
            // line 92
            yield "              <h1><a href=\"";
            yield ($context["home"] ?? null);
            yield "\">";
            yield ($context["name"] ?? null);
            yield "</a></h1>
            ";
        }
        // line 94
        yield "          </div>
        </div>
        <div class=\"col-md-7\">
          <div class=\"scroller\">
              <marquee behavior=\"scroll\" direction=\"left\" scrollamount=\"5\">
                ";
        // line 100
        yield "              </marquee>
          </div>
          <div class=\"account-links-top\">
            <nav id=\"top\">
              <div class=\"container-full\">
                <div class=\"row\">
                  <div class=\"col\">
                    <ul class=\"list-inline\">
                      
                      <li class=\"list-inline-item\">
                        <div class=\"dropdown\">
                          <a href=\"#\" class=\"dropdown-toggle\" data-bs-toggle=\"dropdown\"><i class=\"fa-solid fa-user\"></i> <span class=\"d-none d-lg-inline\">";
        // line 111
        yield ($context["text_account"] ?? null);
        yield "</span> <i class=\"fa-solid fa-caret-down\"></i></a>
                          <ul class=\"dropdown-menu dropdown-menu-right\">
                            ";
        // line 113
        if ( !($context["logged"] ?? null)) {
            // line 114
            yield "                              <li><a href=\"";
            yield ($context["register"] ?? null);
            yield "\" class=\"dropdown-item\">";
            yield ($context["text_register"] ?? null);
            yield "</a></li>
                              <li><a href=\"";
            // line 115
            yield ($context["login"] ?? null);
            yield "\" class=\"dropdown-item\">";
            yield ($context["text_login"] ?? null);
            yield "</a></li>
                            ";
        } else {
            // line 117
            yield "                              <li><a href=\"";
            yield ($context["account"] ?? null);
            yield "\" class=\"dropdown-item\">";
            yield ($context["text_account"] ?? null);
            yield "</a></li>
                              <li><a href=\"";
            // line 118
            yield ($context["order"] ?? null);
            yield "\" class=\"dropdown-item\">";
            yield ($context["text_order"] ?? null);
            yield "</a></li>
                              <li><a href=\"";
            // line 119
            yield ($context["transaction"] ?? null);
            yield "\" class=\"dropdown-item\">";
            yield ($context["text_transaction"] ?? null);
            yield "</a></li>
                              <li><a href=\"";
            // line 120
            yield ($context["download"] ?? null);
            yield "\" class=\"dropdown-item\">";
            yield ($context["text_download"] ?? null);
            yield "</a></li>
                              <li><a href=\"";
            // line 121
            yield ($context["logout"] ?? null);
            yield "\" class=\"dropdown-item\">";
            yield ($context["text_logout"] ?? null);
            yield "</a></li>
                            ";
        }
        // line 123
        yield "                          </ul>
                        </div>
                      </li>
                      <li class=\"list-inline-item\"><a href=\"";
        // line 126
        yield ($context["shopping_cart"] ?? null);
        yield "\" title=\"Cart Page\"><i class=\"fa-solid fa-cart-shopping\"></i> <span class=\"d-none d-lg-inline\">Cart Page</span></a></li>
                      <li class=\"list-inline-item\"><a href=\"";
        // line 127
        yield ($context["checkout"] ?? null);
        yield "\" title=\"";
        yield ($context["text_checkout"] ?? null);
        yield "\"><i class=\"fa-solid fa-share\"></i> <span class=\"d-none d-lg-inline\">";
        yield ($context["text_checkout"] ?? null);
        yield "</span></a></li>
                      <li class=\"list-inline-item cartbtn\"><div id=\"cart\" class=\"col-md-3\">";
        // line 128
        yield ($context["cart"] ?? null);
        yield "</div></li>
                    </ul>
                  </div>
                </div>
              </div>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </header>
  <div class=\"mainmenu\">
    ";
        // line 140
        yield ($context["menu"] ?? null);
        yield "
  </div>
  <main>

<!-- Search Modal -->
<div class=\"modal fade\" id=\"searchModal\" tabindex=\"-1\" aria-labelledby=\"searchModalLabel\" aria-hidden=\"true\">
  <div class=\"modal-dialog modal-dialog-centered\">
    <div class=\"modal-content searchbox\">
      ";
        // line 148
        yield ($context["search"] ?? null);
        yield "
    </div>
  </div>
</div>";
        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "catalog/view/template/common/header.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  401 => 148,  390 => 140,  375 => 128,  367 => 127,  363 => 126,  358 => 123,  351 => 121,  345 => 120,  339 => 119,  333 => 118,  326 => 117,  319 => 115,  312 => 114,  310 => 113,  305 => 111,  292 => 100,  285 => 94,  277 => 92,  265 => 90,  263 => 89,  240 => 71,  220 => 53,  211 => 51,  206 => 50,  195 => 48,  190 => 47,  181 => 45,  176 => 44,  163 => 42,  158 => 41,  152 => 39,  150 => 38,  145 => 36,  141 => 35,  137 => 34,  133 => 33,  130 => 32,  124 => 30,  121 => 29,  115 => 27,  112 => 26,  106 => 24,  104 => 23,  99 => 20,  88 => 18,  84 => 17,  80 => 15,  74 => 13,  71 => 12,  65 => 10,  63 => 9,  59 => 8,  55 => 7,  45 => 2,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<!DOCTYPE html>
<html dir=\"{{ direction }}\" lang=\"{{ lang }}\">
<head>
  <meta charset=\"UTF-8\"/>
  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">
  <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">
  <title>{{ title }}</title>
  <base href=\"{{ base }}\"/>
  {% if description %}
    <meta name=\"description\" content=\"{{ description }}\"/>
  {% endif %}
  {% if keywords %}
    <meta name=\"keywords\" content=\"{{ keywords }}\"/>
  {% endif %}

  <!-- Open Graph (OG) Tags -->
  {% for property, content in og_tags %}
    <meta property=\"{{ property }}\" content=\"{{ content|escape }}\"/>
  {% endfor %}

  <!-- Twitter Card Tags -->
  <meta name=\"twitter:card\" content=\"summary_large_image\"/>
  {% if og_tags['og:title'] %}
    <meta name=\"twitter:title\" content=\"{{ og_tags['og:title']|escape }}\"/>
  {% endif %}
  {% if og_tags['og:description'] %}
    <meta name=\"twitter:description\" content=\"{{ og_tags['og:description']|escape }}\"/>
  {% endif %}
  {% if og_tags['og:image'] %}
    <meta name=\"twitter:image\" content=\"{{ og_tags['og:image']|escape }}\"/>
  {% endif %}

  <script src=\"{{ jquery }}\" type=\"text/javascript\"></script>
  <link href=\"{{ bootstrap }}\" type=\"text/css\" rel=\"stylesheet\" media=\"screen\"/>
  <link href=\"{{ icons }}\" rel=\"stylesheet\" type=\"text/css\"/>
  <link href=\"{{ stylesheet }}\" type=\"text/css\" rel=\"stylesheet\"/>
  <script src=\"catalog/view/javascript/common.js\" type=\"text/javascript\"></script>
  {% if icon %}
    <link rel=\"icon\" href=\"{{ icon }}\" type=\"image/png\">
  {% endif %}
  {% for style in styles %}
    <link href=\"{{ style.href }}\" type=\"text/css\" rel=\"{{ style.rel }}\" media=\"{{ style.media }}\"/>
  {% endfor %}
  {% for script in scripts %}
    <script src=\"{{ script.href }}\" type=\"text/javascript\"></script>
  {% endfor %}
  {% for link in links %}
    <link href=\"{{ link.href }}\" rel=\"{{ link.rel }}\"/>
  {% endfor %}
  {% for analytic in analytics %}
    {{ analytic }}
  {% endfor %}
  <!-- Google tag (gtag.js) -->
  <script async src=\" https://www.googletagmanager.com/gtag/js?id=G-94992TXH1J\"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-94992TXH1J');
 </script>
</head>
<body>
<div id=\"container\">
  <div id=\"alert\"></div>
  <div class=\"topbar\">
    <div class=\"container-fluid\">
      <div class=\"row\">
        <div class=\"col-6 topcontact\">
          <ul class=\"list-inline\">
            <li class=\"list-inline-item\"><a href=\"{{ contact }}\"><i class=\"fa fa-phone\"></i> {{ telephone }}</a></li>
            <li class=\"list-inline-item\"><a href=\"#\"><i class=\"fa-brands fa-facebook-f\"></i></a></li>
          </ul>
        </div>
        <div class=\"col-6\">
          <div class=\"btnoffer\">
            <a href=\"/index.php?route=information/makeoffer\" class=\"btn-offer\">Make a No-Risk Offer</a>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <header>
    <div class=\"container\">
      <div class=\"row\">
        <div class=\"col-md-5\">
          <div id=\"logo\">
            {% if logo %}
              <a href=\"{{ home }}\"><img src=\"{{ logo }}\" title=\"{{ name }}\" alt=\"{{ name }}\" class=\"img-fluid\"/></a>
            {% else %}
              <h1><a href=\"{{ home }}\">{{ name }}</a></h1>
            {% endif %}
          </div>
        </div>
        <div class=\"col-md-7\">
          <div class=\"scroller\">
              <marquee behavior=\"scroll\" direction=\"left\" scrollamount=\"5\">
                {# <span class=\"text\">Place to add scrolling text</span> #}
              </marquee>
          </div>
          <div class=\"account-links-top\">
            <nav id=\"top\">
              <div class=\"container-full\">
                <div class=\"row\">
                  <div class=\"col\">
                    <ul class=\"list-inline\">
                      
                      <li class=\"list-inline-item\">
                        <div class=\"dropdown\">
                          <a href=\"#\" class=\"dropdown-toggle\" data-bs-toggle=\"dropdown\"><i class=\"fa-solid fa-user\"></i> <span class=\"d-none d-lg-inline\">{{ text_account }}</span> <i class=\"fa-solid fa-caret-down\"></i></a>
                          <ul class=\"dropdown-menu dropdown-menu-right\">
                            {% if not logged %}
                              <li><a href=\"{{ register }}\" class=\"dropdown-item\">{{ text_register }}</a></li>
                              <li><a href=\"{{ login }}\" class=\"dropdown-item\">{{ text_login }}</a></li>
                            {% else %}
                              <li><a href=\"{{ account }}\" class=\"dropdown-item\">{{ text_account }}</a></li>
                              <li><a href=\"{{ order }}\" class=\"dropdown-item\">{{ text_order }}</a></li>
                              <li><a href=\"{{ transaction }}\" class=\"dropdown-item\">{{ text_transaction }}</a></li>
                              <li><a href=\"{{ download }}\" class=\"dropdown-item\">{{ text_download }}</a></li>
                              <li><a href=\"{{ logout }}\" class=\"dropdown-item\">{{ text_logout }}</a></li>
                            {% endif %}
                          </ul>
                        </div>
                      </li>
                      <li class=\"list-inline-item\"><a href=\"{{ shopping_cart }}\" title=\"Cart Page\"><i class=\"fa-solid fa-cart-shopping\"></i> <span class=\"d-none d-lg-inline\">Cart Page</span></a></li>
                      <li class=\"list-inline-item\"><a href=\"{{ checkout }}\" title=\"{{ text_checkout }}\"><i class=\"fa-solid fa-share\"></i> <span class=\"d-none d-lg-inline\">{{ text_checkout }}</span></a></li>
                      <li class=\"list-inline-item cartbtn\"><div id=\"cart\" class=\"col-md-3\">{{ cart }}</div></li>
                    </ul>
                  </div>
                </div>
              </div>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </header>
  <div class=\"mainmenu\">
    {{ menu }}
  </div>
  <main>

<!-- Search Modal -->
<div class=\"modal fade\" id=\"searchModal\" tabindex=\"-1\" aria-labelledby=\"searchModalLabel\" aria-hidden=\"true\">
  <div class=\"modal-dialog modal-dialog-centered\">
    <div class=\"modal-content searchbox\">
      {{ search }}
    </div>
  </div>
</div>", "catalog/view/template/common/header.twig", "D:\\wamp64\\www\\pex\\pondexpo\\catalog\\view\\template\\common\\header.twig");
    }
}
