<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* catalog/view/template/common/home.twig */
class __TwigTemplate_26f3baffc38463740621a491e0c161b3 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 1
        yield ($context["header"] ?? null);
        yield "
<div class=\"bannerhome\">
  ";
        // line 3
        yield ($context["content_top"] ?? null);
        yield "
</div>
<div id=\"common-home\" class=\"container\">
  <div class=\"row\">";
        // line 6
        yield ($context["column_left"] ?? null);
        yield "
    <div id=\"content\" class=\"col\">";
        // line 7
        yield ($context["content_bottom"] ?? null);
        yield "</div>
    ";
        // line 8
        yield ($context["column_right"] ?? null);
        yield "</div>
</div>
";
        // line 10
        yield ($context["footer"] ?? null);
        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "catalog/view/template/common/home.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  66 => 10,  61 => 8,  57 => 7,  53 => 6,  47 => 3,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{{ header }}
<div class=\"bannerhome\">
  {{ content_top }}
</div>
<div id=\"common-home\" class=\"container\">
  <div class=\"row\">{{ column_left }}
    <div id=\"content\" class=\"col\">{{ content_bottom }}</div>
    {{ column_right }}</div>
</div>
{{ footer }}", "catalog/view/template/common/home.twig", "D:\\wamp64\\www\\pex\\pondexpo\\catalog\\view\\template\\common\\home.twig");
    }
}
