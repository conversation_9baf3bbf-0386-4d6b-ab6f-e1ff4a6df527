<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* catalog/view/template/checkout/shipping_method.twig */
class __TwigTemplate_1d037a9c046a492738a8dac601c497a3 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 1
        yield "<fieldset>
  <legend>";
        // line 2
        yield ($context["heading_title"] ?? null);
        yield "</legend>
  <div class=\"input-group\">
    <span class=\"input-group-text\"><i class=\"fa fa-truck\"></i></span><input type=\"text\" name=\"shipping_method\" value=\"";
        // line 4
        yield ($context["shipping_method"] ?? null);
        yield "\" placeholder=\"";
        yield ($context["entry_shipping_method"] ?? null);
        yield "\" id=\"input-shipping-method\" class=\"form-control\" readonly/>
    <button type=\"button\" id=\"button-shipping-methods\" class=\"btn btn-primary\">";
        // line 5
        yield ($context["button_choose"] ?? null);
        yield "</button>
  </div>
  <input type=\"hidden\" name=\"code\" value=\"";
        // line 7
        yield ($context["code"] ?? null);
        yield "\" id=\"input-shipping-code\"/>
  <div id=\"error-shipping-method\" class=\"invalid-feedback\"></div>

  <!-- Inline shipping methods container -->
  <div id=\"shipping-methods-container\" class=\"mt-3\" style=\"display: none;\">
    <div class=\"card\">
      <div class=\"card-header\">
        <h6 class=\"mb-0\"><i class=\"fa fa-truck\"></i> ";
        // line 14
        yield ($context["text_shipping_method"] ?? null);
        yield "</h6>
      </div>
      <div class=\"card-body\">
        <form id=\"form-shipping-method\">
          <p>";
        // line 18
        yield ($context["text_shipping"] ?? null);
        yield "</p>
          <div id=\"shipping-methods-list\"></div>
          <div class=\"text-end mt-3\">
            <button type=\"submit\" id=\"button-shipping-method\" class=\"btn btn-primary\">";
        // line 21
        yield ($context["button_continue"] ?? null);
        yield "</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</fieldset>
<script type=\"text/javascript\"><!--
\$('#button-shipping-methods').on('click', function() {
    var element = this;

    chain.attach(function() {
        return \$.ajax({
            url: 'index.php?route=checkout/shipping_method.quote&language=";
        // line 34
        yield ($context["language"] ?? null);
        yield "',
            dataType: 'json',
            beforeSend: function() {
                \$(element).button('loading');
            },
            complete: function() {
                \$(element).button('reset');
            },
            success: function(json) {
                \$('#input-shipping-method').removeClass('is-invalid');
                \$('#error-shipping-method').removeClass('d-block');

                if (json['error']) {
                    \$('#input-shipping-method').addClass('is-invalid');
                    \$('#error-shipping-method').html(json['error']).addClass('d-block');
                }

                if (json['shipping_methods']) {
                    // Check if shipping method was auto-selected
                    if (json['auto_selected'] && json['selected_method']) {
                        // Auto-selected single shipping method
                        \$('#input-shipping-method').val(json['selected_method']['name'] + ' - ' + json['selected_method']['text']);
                        \$('#input-shipping-code').val(json['selected_method']['code']);

                        // Replace the choose button with a checkmark to show it's selected
                        \$('#button-shipping-methods').replaceWith('<span class=\"input-group-text bg-success text-white\"><i class=\"fa fa-check\"></i> Selected</span>');
                        \$('#input-shipping-method').prop('readonly', true);

                        // Load the confirm section since shipping method is already selected
                        \$('#checkout-confirm').load('index.php?route=checkout/confirm.confirm&language=";
        // line 63
        yield ($context["language"] ?? null);
        yield "');
                    } else {
                        // Multiple shipping methods - show selection interface
                        // Clear existing shipping methods
                        \$('#shipping-methods-list').empty();

                        var html = '';
                        var first = true;

                        for (i in json['shipping_methods']) {
                            html += '<p><strong>' + json['shipping_methods'][i]['name'] + '</strong></p>';

                            if (!json['shipping_methods'][i]['error']) {
                                for (j in json['shipping_methods'][i]['quote']) {
                                    html += '<div class=\"form-check\">';

                                    var code = i + '-' + j.replaceAll('_', '-');

                                    html += '<input type=\"radio\" name=\"shipping_method\" value=\"' + json['shipping_methods'][i]['quote'][j]['code'] + '\" id=\"input-shipping-method-' + code + '\"';

                                    var method = \$('#input-shipping-code').val();

                                    if ((json['shipping_methods'][i]['quote'][j]['code'] == method) || (!method && first)) {
                                        html += ' checked';

                                        first = false;
                                    }

                                    html += '/>';
                                    html += '  <label for=\"input-shipping-method-' + code + '\" class=\"form-check-label\">' + json['shipping_methods'][i]['quote'][j]['name'] + ' - ' + json['shipping_methods'][i]['quote'][j]['text'] + '</label>';
                                    html += '</div>';
                                }
                            } else {
                                html += '<div class=\"alert alert-danger\">' + json['shipping_methods'][i]['error'] + '</div>';
                            }
                        }

                        // Populate the inline container and show it
                        \$('#shipping-methods-list').html(html);
                        \$('#shipping-methods-container').show();
                    }
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
            }
        });
    });
});

\$(document).on('submit', '#form-shipping-method', function(e) {
    e.preventDefault();

    var element = this;

    chain.attach(function() {
        return \$.ajax({
            url: 'index.php?route=checkout/shipping_method.save&language=";
        // line 120
        yield ($context["language"] ?? null);
        yield "',
            type: 'post',
            data: \$('#form-shipping-method').serialize(),
            dataType: 'json',
            contentType: 'application/x-www-form-urlencoded',
            beforeSend: function() {
                \$('#button-shipping-method').button('loading');
            },
            complete: function() {
                \$('#button-shipping-method').button('reset');
            },
            success: function(json) {
                console.log(json);

                if (json['redirect']) {
                    location = json['redirect'];
                }

                if (json['error']) {
                    \$('#alert').prepend('<div class=\"alert alert-danger alert-dismissible\"><i class=\"fa-solid fa-circle-exclamation\"></i> ' + json['error'] + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');
                }

                if (json['success']) {
                    \$('#alert').prepend('<div class=\"alert alert-success alert-dismissible\"><i class=\"fa-solid fa-circle-check\"></i> ' + json['success'] + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');

                    // Keep the shipping methods container open - don't hide it
                    // \$('#shipping-methods-container').hide();

                    \$('#input-shipping-method').val(\$('input[name=\\'shipping_method\\']:checked').parent().find('label').text());
                    \$('#input-shipping-code').val(\$('input[name=\\'shipping_method\\']:checked').val());

                    // Don't clear payment method if it's already auto-selected (has a code)
                    if (!\$('#input-payment-code').val()) {
                        \$('#input-payment-method').val('');
                    }

                    \$('#cart').load('index.php?route=common/cart.info&language=";
        // line 156
        yield ($context["language"] ?? null);
        yield "');
                    \$('#checkout-confirm').load('index.php?route=checkout/confirm.confirm&language=";
        // line 157
        yield ($context["language"] ?? null);
        yield "');
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
            }
        });
    });
});

// Check if shipping method is already selected on page load and restore visual state
\$(document).ready(function() {
    if (\$('#input-shipping-method').val() && \$('#input-shipping-code').val()) {
        // Shipping method already selected, style it appropriately
        \$('#button-shipping-methods').replaceWith('<span class=\"input-group-text bg-success text-white\"><i class=\"fa fa-check\"></i> Selected</span>');
    }
});
//--></script>";
        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "catalog/view/template/checkout/shipping_method.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  235 => 157,  231 => 156,  192 => 120,  132 => 63,  100 => 34,  84 => 21,  78 => 18,  71 => 14,  61 => 7,  56 => 5,  50 => 4,  45 => 2,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<fieldset>
  <legend>{{ heading_title }}</legend>
  <div class=\"input-group\">
    <span class=\"input-group-text\"><i class=\"fa fa-truck\"></i></span><input type=\"text\" name=\"shipping_method\" value=\"{{ shipping_method }}\" placeholder=\"{{ entry_shipping_method }}\" id=\"input-shipping-method\" class=\"form-control\" readonly/>
    <button type=\"button\" id=\"button-shipping-methods\" class=\"btn btn-primary\">{{ button_choose }}</button>
  </div>
  <input type=\"hidden\" name=\"code\" value=\"{{ code }}\" id=\"input-shipping-code\"/>
  <div id=\"error-shipping-method\" class=\"invalid-feedback\"></div>

  <!-- Inline shipping methods container -->
  <div id=\"shipping-methods-container\" class=\"mt-3\" style=\"display: none;\">
    <div class=\"card\">
      <div class=\"card-header\">
        <h6 class=\"mb-0\"><i class=\"fa fa-truck\"></i> {{ text_shipping_method }}</h6>
      </div>
      <div class=\"card-body\">
        <form id=\"form-shipping-method\">
          <p>{{ text_shipping }}</p>
          <div id=\"shipping-methods-list\"></div>
          <div class=\"text-end mt-3\">
            <button type=\"submit\" id=\"button-shipping-method\" class=\"btn btn-primary\">{{ button_continue }}</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</fieldset>
<script type=\"text/javascript\"><!--
\$('#button-shipping-methods').on('click', function() {
    var element = this;

    chain.attach(function() {
        return \$.ajax({
            url: 'index.php?route=checkout/shipping_method.quote&language={{ language }}',
            dataType: 'json',
            beforeSend: function() {
                \$(element).button('loading');
            },
            complete: function() {
                \$(element).button('reset');
            },
            success: function(json) {
                \$('#input-shipping-method').removeClass('is-invalid');
                \$('#error-shipping-method').removeClass('d-block');

                if (json['error']) {
                    \$('#input-shipping-method').addClass('is-invalid');
                    \$('#error-shipping-method').html(json['error']).addClass('d-block');
                }

                if (json['shipping_methods']) {
                    // Check if shipping method was auto-selected
                    if (json['auto_selected'] && json['selected_method']) {
                        // Auto-selected single shipping method
                        \$('#input-shipping-method').val(json['selected_method']['name'] + ' - ' + json['selected_method']['text']);
                        \$('#input-shipping-code').val(json['selected_method']['code']);

                        // Replace the choose button with a checkmark to show it's selected
                        \$('#button-shipping-methods').replaceWith('<span class=\"input-group-text bg-success text-white\"><i class=\"fa fa-check\"></i> Selected</span>');
                        \$('#input-shipping-method').prop('readonly', true);

                        // Load the confirm section since shipping method is already selected
                        \$('#checkout-confirm').load('index.php?route=checkout/confirm.confirm&language={{ language }}');
                    } else {
                        // Multiple shipping methods - show selection interface
                        // Clear existing shipping methods
                        \$('#shipping-methods-list').empty();

                        var html = '';
                        var first = true;

                        for (i in json['shipping_methods']) {
                            html += '<p><strong>' + json['shipping_methods'][i]['name'] + '</strong></p>';

                            if (!json['shipping_methods'][i]['error']) {
                                for (j in json['shipping_methods'][i]['quote']) {
                                    html += '<div class=\"form-check\">';

                                    var code = i + '-' + j.replaceAll('_', '-');

                                    html += '<input type=\"radio\" name=\"shipping_method\" value=\"' + json['shipping_methods'][i]['quote'][j]['code'] + '\" id=\"input-shipping-method-' + code + '\"';

                                    var method = \$('#input-shipping-code').val();

                                    if ((json['shipping_methods'][i]['quote'][j]['code'] == method) || (!method && first)) {
                                        html += ' checked';

                                        first = false;
                                    }

                                    html += '/>';
                                    html += '  <label for=\"input-shipping-method-' + code + '\" class=\"form-check-label\">' + json['shipping_methods'][i]['quote'][j]['name'] + ' - ' + json['shipping_methods'][i]['quote'][j]['text'] + '</label>';
                                    html += '</div>';
                                }
                            } else {
                                html += '<div class=\"alert alert-danger\">' + json['shipping_methods'][i]['error'] + '</div>';
                            }
                        }

                        // Populate the inline container and show it
                        \$('#shipping-methods-list').html(html);
                        \$('#shipping-methods-container').show();
                    }
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
            }
        });
    });
});

\$(document).on('submit', '#form-shipping-method', function(e) {
    e.preventDefault();

    var element = this;

    chain.attach(function() {
        return \$.ajax({
            url: 'index.php?route=checkout/shipping_method.save&language={{ language }}',
            type: 'post',
            data: \$('#form-shipping-method').serialize(),
            dataType: 'json',
            contentType: 'application/x-www-form-urlencoded',
            beforeSend: function() {
                \$('#button-shipping-method').button('loading');
            },
            complete: function() {
                \$('#button-shipping-method').button('reset');
            },
            success: function(json) {
                console.log(json);

                if (json['redirect']) {
                    location = json['redirect'];
                }

                if (json['error']) {
                    \$('#alert').prepend('<div class=\"alert alert-danger alert-dismissible\"><i class=\"fa-solid fa-circle-exclamation\"></i> ' + json['error'] + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');
                }

                if (json['success']) {
                    \$('#alert').prepend('<div class=\"alert alert-success alert-dismissible\"><i class=\"fa-solid fa-circle-check\"></i> ' + json['success'] + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');

                    // Keep the shipping methods container open - don't hide it
                    // \$('#shipping-methods-container').hide();

                    \$('#input-shipping-method').val(\$('input[name=\\'shipping_method\\']:checked').parent().find('label').text());
                    \$('#input-shipping-code').val(\$('input[name=\\'shipping_method\\']:checked').val());

                    // Don't clear payment method if it's already auto-selected (has a code)
                    if (!\$('#input-payment-code').val()) {
                        \$('#input-payment-method').val('');
                    }

                    \$('#cart').load('index.php?route=common/cart.info&language={{ language }}');
                    \$('#checkout-confirm').load('index.php?route=checkout/confirm.confirm&language={{ language }}');
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
            }
        });
    });
});

// Check if shipping method is already selected on page load and restore visual state
\$(document).ready(function() {
    if (\$('#input-shipping-method').val() && \$('#input-shipping-code').val()) {
        // Shipping method already selected, style it appropriately
        \$('#button-shipping-methods').replaceWith('<span class=\"input-group-text bg-success text-white\"><i class=\"fa fa-check\"></i> Selected</span>');
    }
});
//--></script>", "catalog/view/template/checkout/shipping_method.twig", "D:\\wamp64\\www\\pex\\pondexpo\\catalog\\view\\template\\checkout\\shipping_method.twig");
    }
}
