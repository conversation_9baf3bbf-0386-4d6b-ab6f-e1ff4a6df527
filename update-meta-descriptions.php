<?php
// Database connection parameters
$servername = "localhost"; // Replace with your DB server
$username = "root"; // Replace with your DB username
$password = "prb24prb"; // Replace with your DB password
$dbname = "pondexpo"; // Replace with your DB name

// Path to the JSON file
$jsonFile = "meta_descriptions.json"; // Replace with your JSON file path

try {
    // Read the JSON file
    $jsonData = file_get_contents($jsonFile);
    if ($jsonData === false) {
        throw new Exception("Error reading JSON file: $jsonFile");
    }

    // Decode JSON data into an array
    $data = json_decode($jsonData, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("JSON decode error: " . json_last_error_msg());
    }

    // Create a PDO connection to the database
    $pdo = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Prepare the UPDATE statement
    $stmt = $pdo->prepare("UPDATE pexpst27_product_description SET meta_description = :meta_description WHERE name = :name");
    // If using product_id instead of name, replace 'name' with 'product_id' in the query

    // Begin a transaction
    $pdo->beginTransaction();

    // Loop through JSON data and update the table
    foreach ($data as $item) {
        if (!isset($item['name']) || !isset($item['meta_description'])) {
            echo "Skipping invalid entry: " . json_encode($item) . "\n";
            continue;
        }

        $stmt->execute([
            ':name' => $item['name'],
            ':meta_description' => $item['meta_description']
        ]);

        echo "Updated meta_description for name: {$item['name']}\n";
    }

    // Commit the transaction
    $pdo->commit();
    echo "All updates completed successfully.\n";

} catch (Exception $e) {
    // Roll back the transaction on error
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo "Error: " . $e->getMessage() . "\n";
}

// Close the PDO connection
$pdo = null;
?>