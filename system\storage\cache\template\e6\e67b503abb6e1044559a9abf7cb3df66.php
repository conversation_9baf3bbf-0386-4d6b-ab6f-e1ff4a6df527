<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* catalog/view/template/product/product.twig */
class __TwigTemplate_9bff4839d072225e7981122a54f75ba9 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 1
        yield ($context["header"] ?? null);
        yield "
<div id=\"product-info\" class=\"container\">
\t<ul class=\"breadcrumb\">
\t\t";
        // line 4
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(($context["breadcrumbs"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["breadcrumb"]) {
            // line 5
            yield "\t\t\t<li class=\"breadcrumb-item\">
\t\t\t\t<a href=\"";
            // line 6
            yield CoreExtension::getAttribute($this->env, $this->source, $context["breadcrumb"], "href", [], "any", false, false, false, 6);
            yield "\">";
            yield CoreExtension::getAttribute($this->env, $this->source, $context["breadcrumb"], "text", [], "any", false, false, false, 6);
            yield "</a>
\t\t\t</li>
\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['breadcrumb'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 9
        yield "\t</ul>
\t<div class=\"row\">";
        // line 10
        yield ($context["column_left"] ?? null);
        yield "
\t\t<div id=\"content\" class=\"col\">
\t\t\t";
        // line 12
        yield ($context["content_top"] ?? null);
        yield "
\t\t\t<div class=\"row mb-3\">
\t\t\t\t";
        // line 14
        if ((($context["thumb"] ?? null) || ($context["images"] ?? null))) {
            // line 15
            yield "          <div class=\"col-sm\">
            <div class=\"image magnific-popup\">
              ";
            // line 17
            if (($context["thumb"] ?? null)) {
                // line 18
                yield "                <a href=\"";
                yield ($context["popup"] ?? null);
                yield "\" title=\"";
                yield ($context["heading_title"] ?? null);
                yield "\"><img src=\"";
                yield ($context["thumb"] ?? null);
                yield "\" title=\"";
                yield ($context["heading_title"] ?? null);
                yield "\" alt=\"";
                yield ($context["heading_title"] ?? null);
                yield "\" class=\"img-thumbnail mb-3\"/></a>
              ";
            }
            // line 20
            yield "              ";
            if (($context["images"] ?? null)) {
                // line 21
                yield "                <div class=\"thumbnails\">
                  ";
                // line 22
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable(($context["images"] ?? null));
                foreach ($context['_seq'] as $context["_key"] => $context["image"]) {
                    // line 23
                    yield "                    <a href=\"";
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["image"], "popup", [], "any", false, false, false, 23);
                    yield "\" title=\"";
                    yield ($context["heading_title"] ?? null);
                    yield "\"><img src=\"";
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["image"], "thumb", [], "any", false, false, false, 23);
                    yield "\" title=\"";
                    yield ($context["heading_title"] ?? null);
                    yield "\" alt=\"";
                    yield ($context["heading_title"] ?? null);
                    yield "\" class=\"img-thumbnail\"/></a>&nbsp;
                  ";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['image'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 25
                yield "                </div>
              ";
            }
            // line 27
            yield "            </div>
          </div>
        ";
        }
        // line 30
        yield "\t\t\t\t<div class=\"col-md-7\">
\t\t\t\t\t<h1>";
        // line 31
        yield ($context["heading_title"] ?? null);
        yield "</h1>
\t\t\t\t\t";
        // line 32
        if (($context["additional_info1"] ?? null)) {
            // line 33
            yield "\t\t\t\t\t\t";
            yield ($context["additional_info1"] ?? null);
            yield "
\t\t\t\t\t";
        }
        // line 35
        yield "\t\t\t\t\t<ul class=\"list-unstyled\">
\t\t\t\t\t\t<li>";
        // line 36
        yield ($context["text_model"] ?? null);
        yield "
\t\t\t\t\t\t\t<span class=\"primary\">
\t\t\t\t\t\t\t\t<strong>";
        // line 38
        yield ($context["model"] ?? null);
        yield "</strong>
\t\t\t\t\t\t\t</span>
\t\t\t\t\t\t</li>
\t\t\t\t\t\t<li>";
        // line 41
        yield ($context["text_stock"] ?? null);
        yield "
\t\t\t\t\t\t\t<span class=\"green\">";
        // line 42
        yield ($context["stock"] ?? null);
        yield "</span>
\t\t\t\t\t\t</li>
\t\t\t\t\t</ul>
\t\t\t\t\t<div class=\"row\">
\t\t\t\t\t\t<div class=\"col-md-5\">
\t\t\t\t\t\t\t";
        // line 47
        if (($context["price"] ?? null)) {
            // line 48
            yield "\t\t\t\t\t\t\t\t<ul class=\"list-unstyled\">
\t\t\t\t\t\t\t\t\t";
            // line 49
            if ( !($context["special"] ?? null)) {
                // line 50
                yield "\t\t\t\t\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t\t\t\t\t<h2>
\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"price-new\">";
                // line 52
                yield ($context["price"] ?? null);
                yield "</span>
\t\t\t\t\t\t\t\t\t\t\t</h2>
\t\t\t\t\t\t\t\t\t\t</li>
\t\t\t\t\t\t\t\t\t";
            } else {
                // line 56
                yield "\t\t\t\t\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t\t\t\t\t<h2>
\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"price-old\">";
                // line 58
                yield ($context["price"] ?? null);
                yield "</span>
\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"price-new\">";
                // line 59
                yield ($context["special"] ?? null);
                yield "</span>
\t\t\t\t\t\t\t\t\t\t\t</h2>
\t\t\t\t\t\t\t\t\t\t</li>
\t\t\t\t\t\t\t\t\t";
            }
            // line 63
            yield "\t\t\t\t\t\t\t\t\t";
            if (($context["tax"] ?? null)) {
                // line 64
                yield "\t\t\t\t\t\t\t\t\t\t<li>";
                yield ($context["text_tax"] ?? null);
                yield "
\t\t\t\t\t\t\t\t\t\t\t";
                // line 65
                yield ($context["tax"] ?? null);
                yield "</li>
\t\t\t\t\t\t\t\t\t";
            }
            // line 67
            yield "\t\t\t\t\t\t\t\t\t";
            if (($context["points"] ?? null)) {
                // line 68
                yield "\t\t\t\t\t\t\t\t\t\t<li>";
                yield ($context["text_points"] ?? null);
                yield "
\t\t\t\t\t\t\t\t\t\t\t";
                // line 69
                yield ($context["points"] ?? null);
                yield "</li>
\t\t\t\t\t\t\t\t\t";
            }
            // line 71
            yield "\t\t\t\t\t\t\t\t\t";
            if (($context["discounts"] ?? null)) {
                // line 72
                yield "\t\t\t\t\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t\t\t\t\t<hr>
\t\t\t\t\t\t\t\t\t\t</li>
\t\t\t\t\t\t\t\t\t\t";
                // line 75
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable(($context["discounts"] ?? null));
                foreach ($context['_seq'] as $context["_key"] => $context["discount"]) {
                    // line 76
                    yield "\t\t\t\t\t\t\t\t\t\t\t<li>";
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["discount"], "quantity", [], "any", false, false, false, 76);
                    yield ($context["text_discount"] ?? null);
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["discount"], "price", [], "any", false, false, false, 76);
                    yield "</li>
\t\t\t\t\t\t\t\t\t\t";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['discount'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 78
                yield "\t\t\t\t\t\t\t\t\t";
            }
            // line 79
            yield "\t\t\t\t\t\t\t\t</ul>
\t\t\t\t\t\t\t";
        }
        // line 81
        yield "\t\t\t\t\t\t</div>
\t\t\t\t\t\t<div class=\"col-md-7 ps-4\">
\t\t\t\t\t\t\t<form method=\"post\" data-oc-toggle=\"ajax\">
\t\t\t\t\t\t\t\t<div class=\"btn-group\">
\t\t\t\t\t\t\t\t\t<button type=\"submit\" formaction=\"";
        // line 85
        yield ($context["wishlist_add"] ?? null);
        yield "\" data-bs-toggle=\"tooltip\" class=\"btn btn-light btn-md\" title=\"";
        yield ($context["button_wishlist"] ?? null);
        yield "\">
\t\t\t\t\t\t\t\t\t\t<i class=\"fa-solid fa-heart\"></i>
\t\t\t\t\t\t\t\t\t</button>
\t\t\t\t\t\t\t\t\t<button type=\"submit\" formaction=\"";
        // line 88
        yield ($context["compare_add"] ?? null);
        yield "\" data-bs-toggle=\"tooltip\" class=\"btn btn-light btn-md\" title=\"";
        yield ($context["button_compare"] ?? null);
        yield "\">
\t\t\t\t\t\t\t\t\t\t<i class=\"fa-solid fa-arrow-right-arrow-left\"></i>
\t\t\t\t\t\t\t\t\t</button>
\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t<input type=\"hidden\" name=\"product_id\" value=\"";
        // line 92
        yield ($context["product_id"] ?? null);
        yield "\"/>
\t\t\t\t\t\t\t</form>
\t\t\t\t\t\t</div>
\t\t\t\t\t</div>
\t\t\t\t\t<div class=\"row\">
\t\t\t\t\t\t<div class=\"col-md-7\">
\t\t\t\t\t\t\t";
        // line 98
        if ((($context["stock"] ?? null) == "In Stock")) {
            // line 99
            yield "\t\t\t\t\t\t\t\t<div id=\"product col-md-6\">
\t\t\t\t\t\t\t\t\t<form id=\"form-product\">
\t\t\t\t\t\t\t\t\t\t";
            // line 101
            if (($context["options"] ?? null)) {
                // line 102
                yield "\t\t\t\t\t\t\t\t\t\t\t<hr>
\t\t\t\t\t\t\t\t\t\t\t<h3>";
                // line 103
                yield ($context["text_option"] ?? null);
                yield "</h3>
\t\t\t\t\t\t\t\t\t\t\t<div class=\"prodoptions\">
\t\t\t\t\t\t\t\t\t\t\t\t";
                // line 105
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable(($context["options"] ?? null));
                foreach ($context['_seq'] as $context["_key"] => $context["option"]) {
                    // line 106
                    yield "\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    if ((CoreExtension::getAttribute($this->env, $this->source, $context["option"], "type", [], "any", false, false, false, 106) == "select")) {
                        // line 107
                        yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3";
                        if (CoreExtension::getAttribute($this->env, $this->source, $context["option"], "required", [], "any", false, false, false, 107)) {
                            yield " required";
                        }
                        yield "\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-";
                        // line 108
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 108);
                        yield "\" class=\"form-label\">";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "name", [], "any", false, false, false, 108);
                        yield "</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<select name=\"option[";
                        // line 109
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 109);
                        yield "]\" id=\"input-option-";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 109);
                        yield "\" class=\"form-select\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value=\"\">";
                        // line 110
                        yield ($context["text_select"] ?? null);
                        yield "</option>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                        // line 111
                        $context['_parent'] = $context;
                        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_value", [], "any", false, false, false, 111));
                        foreach ($context['_seq'] as $context["_key"] => $context["option_value"]) {
                            // line 112
                            yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value=\"";
                            yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "product_option_value_id", [], "any", false, false, false, 112);
                            yield "\" 
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            // line 113
                            if (CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "image", [], "any", false, false, false, 113)) {
                                // line 114
                                yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata-option-image=\"";
                                yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "image", [], "any", false, false, false, 114);
                                yield "\"
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata-option-image-popup=\"";
                                // line 115
                                yield ((CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "popup_image", [], "any", true, true, false, 115)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "popup_image", [], "any", false, false, false, 115), CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "image", [], "any", false, false, false, 115))) : (CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "image", [], "any", false, false, false, 115)));
                                yield "\"
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            }
                            // line 116
                            yield ">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            // line 117
                            yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "name", [], "any", false, false, false, 117);
                            yield "
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            // line 118
                            if (CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price", [], "any", false, false, false, 118)) {
                                // line 119
                                yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t(";
                                yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price_prefix", [], "any", false, false, false, 119);
                                yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price", [], "any", false, false, false, 119);
                                yield ")
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            }
                            // line 121
                            yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</option>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                        }
                        $_parent = $context['_parent'];
                        unset($context['_seq'], $context['_key'], $context['option_value'], $context['_parent']);
                        $context = array_intersect_key($context, $_parent) + $_parent;
                        // line 123
                        yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</select>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-";
                        // line 124
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 124);
                        yield "\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    }
                    // line 127
                    yield "
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    // line 128
                    if ((CoreExtension::getAttribute($this->env, $this->source, $context["option"], "type", [], "any", false, false, false, 128) == "radio")) {
                        // line 129
                        yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3";
                        if (CoreExtension::getAttribute($this->env, $this->source, $context["option"], "required", [], "any", false, false, false, 129)) {
                            yield " required";
                        }
                        yield "\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label class=\"form-label\">";
                        // line 130
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "name", [], "any", false, false, false, 130);
                        yield "</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"input-option-";
                        // line 131
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 131);
                        yield "\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                        // line 132
                        $context['_parent'] = $context;
                        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_value", [], "any", false, false, false, 132));
                        foreach ($context['_seq'] as $context["_key"] => $context["option_value"]) {
                            // line 133
                            yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"form-check option-image\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"radio\" 
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tname=\"option[";
                            // line 135
                            yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 135);
                            yield "]\" 
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue=\"";
                            // line 136
                            yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "product_option_value_id", [], "any", false, false, false, 136);
                            yield "\" 
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"input-option-value-";
                            // line 137
                            yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "product_option_value_id", [], "any", false, false, false, 137);
                            yield "\" 
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"form-check-input\"
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            // line 139
                            if (CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "image", [], "any", false, false, false, 139)) {
                                // line 140
                                yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata-option-image=\"";
                                yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "image", [], "any", false, false, false, 140);
                                yield "\"
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata-option-image-popup=\"";
                                // line 141
                                yield ((CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "popup_image", [], "any", true, true, false, 141)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "popup_image", [], "any", false, false, false, 141), CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "image", [], "any", false, false, false, 141))) : (CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "image", [], "any", false, false, false, 141)));
                                yield "\"
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            }
                            // line 143
                            yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-value-";
                            // line 144
                            yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "product_option_value_id", [], "any", false, false, false, 144);
                            yield "\" class=\"form-check-label\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            // line 145
                            if (CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "image", [], "any", false, false, false, 145)) {
                                // line 146
                                yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img src=\"";
                                yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "image", [], "any", false, false, false, 146);
                                yield "\" alt=\"";
                                yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "name", [], "any", false, false, false, 146);
                                yield " ";
                                if (CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price", [], "any", false, false, false, 146)) {
                                    yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price_prefix", [], "any", false, false, false, 146);
                                    yield " ";
                                    yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price", [], "any", false, false, false, 146);
                                }
                                yield "\" class=\"img-thumbnail\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            }
                            // line 148
                            yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "name", [], "any", false, false, false, 148);
                            yield "
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            // line 149
                            if (CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price", [], "any", false, false, false, 149)) {
                                // line 150
                                yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t(";
                                yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price_prefix", [], "any", false, false, false, 150);
                                yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price", [], "any", false, false, false, 150);
                                yield ")
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            }
                            // line 152
                            yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                        }
                        $_parent = $context['_parent'];
                        unset($context['_seq'], $context['_key'], $context['option_value'], $context['_parent']);
                        $context = array_intersect_key($context, $_parent) + $_parent;
                        // line 155
                        yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-";
                        // line 156
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 156);
                        yield "\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    }
                    // line 159
                    yield "
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    // line 160
                    if ((CoreExtension::getAttribute($this->env, $this->source, $context["option"], "type", [], "any", false, false, false, 160) == "checkbox")) {
                        // line 161
                        yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3";
                        if (CoreExtension::getAttribute($this->env, $this->source, $context["option"], "required", [], "any", false, false, false, 161)) {
                            yield " required";
                        }
                        yield "\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label class=\"form-label\">";
                        // line 162
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "name", [], "any", false, false, false, 162);
                        yield "</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"input-option-";
                        // line 163
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 163);
                        yield "\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                        // line 164
                        $context['_parent'] = $context;
                        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_value", [], "any", false, false, false, 164));
                        foreach ($context['_seq'] as $context["_key"] => $context["option_value"]) {
                            // line 165
                            yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"form-check option-image\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"checkbox\" name=\"option[";
                            // line 166
                            yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 166);
                            yield "][]\" value=\"";
                            yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "product_option_value_id", [], "any", false, false, false, 166);
                            yield "\" id=\"input-option-value-";
                            yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "product_option_value_id", [], "any", false, false, false, 166);
                            yield "\" class=\"form-check-input\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-value-";
                            // line 167
                            yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "product_option_value_id", [], "any", false, false, false, 167);
                            yield "\" class=\"form-check-label\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            // line 168
                            if (CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "image", [], "any", false, false, false, 168)) {
                                // line 169
                                yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img src=\"";
                                yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "image", [], "any", false, false, false, 169);
                                yield "\" alt=\"";
                                yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "name", [], "any", false, false, false, 169);
                                yield " ";
                                if (CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price", [], "any", false, false, false, 169)) {
                                    yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price_prefix", [], "any", false, false, false, 169);
                                    yield " ";
                                    yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price", [], "any", false, false, false, 169);
                                }
                                yield "\" class=\"img-thumbnail\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            }
                            // line 171
                            yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "name", [], "any", false, false, false, 171);
                            yield "
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            // line 172
                            if (CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price", [], "any", false, false, false, 172)) {
                                // line 173
                                yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t(";
                                yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price_prefix", [], "any", false, false, false, 173);
                                yield CoreExtension::getAttribute($this->env, $this->source, $context["option_value"], "price", [], "any", false, false, false, 173);
                                yield ")
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                            }
                            // line 175
                            yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                        }
                        $_parent = $context['_parent'];
                        unset($context['_seq'], $context['_key'], $context['option_value'], $context['_parent']);
                        $context = array_intersect_key($context, $_parent) + $_parent;
                        // line 178
                        yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-";
                        // line 179
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 179);
                        yield "\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    }
                    // line 182
                    yield "
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    // line 183
                    if ((CoreExtension::getAttribute($this->env, $this->source, $context["option"], "type", [], "any", false, false, false, 183) == "text")) {
                        // line 184
                        yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3";
                        if (CoreExtension::getAttribute($this->env, $this->source, $context["option"], "required", [], "any", false, false, false, 184)) {
                            yield " required";
                        }
                        yield "\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-";
                        // line 185
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 185);
                        yield "\" class=\"form-label\">";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "name", [], "any", false, false, false, 185);
                        yield "</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" name=\"option[";
                        // line 186
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 186);
                        yield "]\" value=\"";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "value", [], "any", false, false, false, 186);
                        yield "\" placeholder=\"";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "name", [], "any", false, false, false, 186);
                        yield "\" id=\"input-option-";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 186);
                        yield "\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-";
                        // line 187
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 187);
                        yield "\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    }
                    // line 190
                    yield "
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    // line 191
                    if ((CoreExtension::getAttribute($this->env, $this->source, $context["option"], "type", [], "any", false, false, false, 191) == "textarea")) {
                        // line 192
                        yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3";
                        if (CoreExtension::getAttribute($this->env, $this->source, $context["option"], "required", [], "any", false, false, false, 192)) {
                            yield " required";
                        }
                        yield "\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-";
                        // line 193
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 193);
                        yield "\" class=\"form-label\">";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "name", [], "any", false, false, false, 193);
                        yield "</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<textarea name=\"option[";
                        // line 194
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 194);
                        yield "]\" rows=\"5\" placeholder=\"";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "name", [], "any", false, false, false, 194);
                        yield "\" id=\"input-option-";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 194);
                        yield "\" class=\"form-control\">";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "value", [], "any", false, false, false, 194);
                        yield "</textarea>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-";
                        // line 195
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 195);
                        yield "\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    }
                    // line 198
                    yield "
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    // line 199
                    if ((CoreExtension::getAttribute($this->env, $this->source, $context["option"], "type", [], "any", false, false, false, 199) == "file")) {
                        // line 200
                        yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3";
                        if (CoreExtension::getAttribute($this->env, $this->source, $context["option"], "required", [], "any", false, false, false, 200)) {
                            yield " required";
                        }
                        yield "\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"button-upload-";
                        // line 201
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 201);
                        yield "\" class=\"form-label\">";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "name", [], "any", false, false, false, 201);
                        yield "</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<button type=\"button\" id=\"button-upload-";
                        // line 203
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 203);
                        yield "\" data-oc-toggle=\"upload\" data-oc-url=\"";
                        yield ($context["upload"] ?? null);
                        yield "\" data-oc-target=\"#input-option-";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 203);
                        yield "\" data-oc-size-max=\"";
                        yield ($context["config_file_max_size"] ?? null);
                        yield "\" data-oc-size-error=\"";
                        yield ($context["error_upload_size"] ?? null);
                        yield "\" class=\"btn btn-light btn-block\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<i class=\"fa-solid fa-upload\"></i>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                        // line 205
                        yield ($context["button_upload"] ?? null);
                        yield "</button>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"hidden\" name=\"option[";
                        // line 206
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 206);
                        yield "]\" value=\"\" id=\"input-option-";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 206);
                        yield "\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-";
                        // line 208
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 208);
                        yield "\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    }
                    // line 211
                    yield "
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    // line 212
                    if ((CoreExtension::getAttribute($this->env, $this->source, $context["option"], "type", [], "any", false, false, false, 212) == "date")) {
                        // line 213
                        yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3";
                        if (CoreExtension::getAttribute($this->env, $this->source, $context["option"], "required", [], "any", false, false, false, 213)) {
                            yield " required";
                        }
                        yield "\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-";
                        // line 214
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 214);
                        yield "\" class=\"form-label\">";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "name", [], "any", false, false, false, 214);
                        yield "</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"date\" name=\"option[";
                        // line 215
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 215);
                        yield "]\" value=\"";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "value", [], "any", false, false, false, 215);
                        yield "\" id=\"input-option-";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 215);
                        yield "\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-";
                        // line 216
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 216);
                        yield "\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    }
                    // line 219
                    yield "
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    // line 220
                    if ((CoreExtension::getAttribute($this->env, $this->source, $context["option"], "type", [], "any", false, false, false, 220) == "time")) {
                        // line 221
                        yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3";
                        if (CoreExtension::getAttribute($this->env, $this->source, $context["option"], "required", [], "any", false, false, false, 221)) {
                            yield " required";
                        }
                        yield "\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-";
                        // line 222
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 222);
                        yield "\" class=\"form-label\">";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "name", [], "any", false, false, false, 222);
                        yield "</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"time\" name=\"option[";
                        // line 223
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 223);
                        yield "]\" value=\"";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "value", [], "any", false, false, false, 223);
                        yield "\" id=\"input-option-";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 223);
                        yield "\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-";
                        // line 224
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 224);
                        yield "\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    }
                    // line 227
                    yield "
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    // line 228
                    if ((CoreExtension::getAttribute($this->env, $this->source, $context["option"], "type", [], "any", false, false, false, 228) == "datetime")) {
                        // line 229
                        yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3";
                        if (CoreExtension::getAttribute($this->env, $this->source, $context["option"], "required", [], "any", false, false, false, 229)) {
                            yield " required";
                        }
                        yield "\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-";
                        // line 230
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 230);
                        yield "\" class=\"form-label\">";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "name", [], "any", false, false, false, 230);
                        yield "</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"datetime-local\" name=\"option[";
                        // line 231
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 231);
                        yield "]\" value=\"";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "value", [], "any", false, false, false, 231);
                        yield "\" id=\"input-option-";
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 231);
                        yield "\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-";
                        // line 232
                        yield CoreExtension::getAttribute($this->env, $this->source, $context["option"], "product_option_id", [], "any", false, false, false, 232);
                        yield "\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                    }
                    // line 235
                    yield "\t\t\t\t\t\t\t\t\t\t\t\t";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['option'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 236
                yield "\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t";
            }
            // line 238
            yield "\t\t\t\t\t\t\t\t\t\t";
            if (($context["subscription_plans"] ?? null)) {
                // line 239
                yield "\t\t\t\t\t\t\t\t\t\t\t<hr/>
\t\t\t\t\t\t\t\t\t\t\t<h3>";
                // line 240
                yield ($context["text_subscription"] ?? null);
                yield "</h3>
\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3 required\">
\t\t\t\t\t\t\t\t\t\t\t\t<select name=\"subscription_plan_id\" id=\"input-subscription\" class=\"form-select\">
\t\t\t\t\t\t\t\t\t\t\t\t\t<option value=\"\">";
                // line 243
                yield ($context["text_select"] ?? null);
                yield "</option>
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                // line 244
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable(($context["subscription_plans"] ?? null));
                foreach ($context['_seq'] as $context["_key"] => $context["subscription_plan"]) {
                    // line 245
                    yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value=\"";
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["subscription_plan"], "subscription_plan_id", [], "any", false, false, false, 245);
                    yield "\">";
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["subscription_plan"], "name", [], "any", false, false, false, 245);
                    yield "</option>
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['subscription_plan'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 247
                yield "\t\t\t\t\t\t\t\t\t\t\t\t</select>
\t\t\t\t\t\t\t\t\t\t\t\t";
                // line 248
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable(($context["subscription_plans"] ?? null));
                foreach ($context['_seq'] as $context["_key"] => $context["subscription_plan"]) {
                    // line 249
                    yield "\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"subscription-description-";
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["subscription_plan"], "subscription_plan_id", [], "any", false, false, false, 249);
                    yield "\" class=\"form-text subscription d-none\">";
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["subscription_plan"], "description", [], "any", false, false, false, 249);
                    yield "</div>
\t\t\t\t\t\t\t\t\t\t\t\t";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['subscription_plan'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 251
                yield "\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-subscription\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t";
            }
            // line 254
            yield "\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3 qtybox\">
\t\t\t\t\t\t\t\t\t\t\t<div class=\"input-group\">
\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"input-group-text\">";
            // line 256
            yield ($context["entry_qty"] ?? null);
            yield "</div>
\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"number\" name=\"quantity\" value=\"";
            // line 257
            yield ($context["minimum"] ?? null);
            yield "\" size=\"2\" id=\"input-quantity\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t\t\t<button type=\"submit\" id=\"button-cart\" class=\"btn btn-primary btn-md btn-block\">";
            // line 258
            yield ($context["button_cart"] ?? null);
            yield "</button>
\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t<input type=\"hidden\" name=\"product_id\" value=\"";
            // line 260
            yield ($context["product_id"] ?? null);
            yield "\" id=\"input-product-id\"/>
\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-quantity\" class=\"form-text\"></div>
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t";
            // line 263
            if ((($context["minimum"] ?? null) > 1)) {
                // line 264
                yield "\t\t\t\t\t\t\t\t\t\t\t<div class=\"alert alert-warning\">
\t\t\t\t\t\t\t\t\t\t\t\t<i class=\"fa-solid fa-circle-info\"></i>
\t\t\t\t\t\t\t\t\t\t\t\t";
                // line 266
                yield ($context["text_minimum"] ?? null);
                yield "</div>
\t\t\t\t\t\t\t\t\t\t";
            }
            // line 268
            yield "\t\t\t\t\t\t\t\t\t</form>
\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t";
        } else {
            // line 271
            yield "\t\t\t\t\t\t\t\t<p class=\"mt-3\"><span class=\"text-danger\"><strong>Product Currently Out of Stock</strong></span></p>
\t\t\t\t\t\t\t\t<p>Please use enquiry form below if interested.</p>
\t\t\t\t\t\t\t\t<form id=\"form-enquiry\" class=\"product-enquiry\">
\t\t\t\t\t\t\t\t\t<h3>Enquiry for: ";
            // line 274
            yield ($context["heading_title"] ?? null);
            yield "</h3>
\t\t\t\t\t\t\t\t\t<fieldset>
\t\t\t\t\t\t\t\t\t\t<legend>";
            // line 276
            yield ($context["text_contact"] ?? null);
            yield "</legend>
\t\t\t\t\t\t\t\t\t\t<input type=\"hidden\" name=\"emailsub\" value=\"";
            // line 277
            yield ($context["heading_title"] ?? null);
            yield "\" id=\"input-emailsub\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t<div class=\"row mb-3 required\">
\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-name\" class=\"col-sm-3 col-form-label\">";
            // line 279
            yield ($context["entry_name"] ?? null);
            yield "</label>
\t\t\t\t\t\t\t\t\t\t\t<div class=\"col-sm-9\">
\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" name=\"name\" value=\"";
            // line 281
            yield ($context["name"] ?? null);
            yield "\" id=\"input-name\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-name\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t<div class=\"row mb-3 required\">
\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-email\" class=\"col-sm-3 col-form-label\">";
            // line 286
            yield ($context["entry_email"] ?? null);
            yield "</label>
\t\t\t\t\t\t\t\t\t\t\t<div class=\"col-sm-9\">
\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" name=\"email\" value=\"";
            // line 288
            yield ($context["email"] ?? null);
            yield "\" id=\"input-email\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-email\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t<div class=\"row mb-3 required\">
\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-enquiry\" class=\"col-sm-3 col-form-label\">";
            // line 293
            yield ($context["entry_enquiry"] ?? null);
            yield "</label>
\t\t\t\t\t\t\t\t\t\t\t<div class=\"col-sm-9\">
\t\t\t\t\t\t\t\t\t\t\t\t<textarea name=\"enquiry\" rows=\"2\" id=\"input-enquiry\" class=\"form-control\"></textarea>
\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-enquiry\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t";
            // line 299
            yield ($context["captcha"] ?? null);
            yield "
\t\t\t\t\t\t\t\t\t</fieldset>
\t\t\t\t\t\t\t\t\t<div class=\"text-end\">
\t\t\t\t\t\t\t\t\t\t<button type=\"submit\" class=\"btn btn-primary\">";
            // line 302
            yield ($context["button_enquiry"] ?? null);
            yield "</button>
\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t</form>
\t\t\t\t\t\t\t\t";
        }
        // line 306
        yield "\t\t\t\t\t\t</div>
\t\t\t\t\t\t";
        // line 308
        yield "\t\t\t\t\t</div>
\t\t\t\t\t<hr>
\t\t\t\t\t";
        // line 311
        yield "\t\t\t\t\t<div class=\"prodinfo\">
\t\t\t\t\t\t<div class=\"accordion\" id=\"productInfoAccordion\">
\t\t\t\t\t\t\t<div class=\"accordion-item\">
\t\t\t\t\t\t\t\t<h2 class=\"accordion-header\">
\t\t\t\t\t\t\t\t\t<button class=\"accordion-button\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#productInfoOne\" aria-expanded=\"true\" aria-controls=\"productInfoOne\">
\t\t\t\t\t\t\t\t\t\t";
        // line 316
        yield ($context["tab_description"] ?? null);
        yield "
\t\t\t\t\t\t\t\t\t</button>
\t\t\t\t\t\t\t\t</h2>
\t\t\t\t\t\t\t\t<div id=\"productInfoOne\" class=\"accordion-collapse collapse show\" data-bs-parent=\"#productInfoAccordion\">
\t\t\t\t\t\t\t\t\t<div class=\"accordion-body\">
\t\t\t\t\t\t\t\t\t\t";
        // line 321
        yield ($context["description"] ?? null);
        yield "
\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t";
        // line 325
        if (($context["attribute_groups"] ?? null)) {
            // line 326
            yield "\t\t\t\t\t\t\t\t<div class=\"accordion-item\">
\t\t\t\t\t\t\t\t\t<h2 class=\"accordion-header\">
\t\t\t\t\t\t\t\t\t\t<button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#productInfoFive\" aria-expanded=\"false\" aria-controls=\"productInfoFive\">
\t\t\t\t\t\t\t\t\t\t\t";
            // line 329
            yield ($context["tab_attribute"] ?? null);
            yield "
\t\t\t\t\t\t\t\t\t\t</button>
\t\t\t\t\t\t\t\t\t</h2>
\t\t\t\t\t\t\t\t\t<div id=\"productInfoFive\" class=\"accordion-collapse collapse\" data-bs-parent=\"#productInfoAccordion\">
\t\t\t\t\t\t\t\t\t\t<div class=\"accordion-body\">
\t\t\t\t\t\t\t\t\t\t\t<table class=\"table table-bordered\">
\t\t\t\t\t\t\t\t\t\t\t\t";
            // line 335
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(($context["attribute_groups"] ?? null));
            foreach ($context['_seq'] as $context["_key"] => $context["attribute_group"]) {
                // line 336
                yield "\t\t\t\t\t\t\t\t\t\t\t\t\t<thead>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t<tr>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td colspan=\"2\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<strong>";
                // line 339
                yield CoreExtension::getAttribute($this->env, $this->source, $context["attribute_group"], "name", [], "any", false, false, false, 339);
                yield "</strong>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</td>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</tr>
\t\t\t\t\t\t\t\t\t\t\t\t\t</thead>
\t\t\t\t\t\t\t\t\t\t\t\t\t<tbody>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                // line 344
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["attribute_group"], "attribute", [], "any", false, false, false, 344));
                foreach ($context['_seq'] as $context["_key"] => $context["attribute"]) {
                    // line 345
                    yield "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<tr>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>";
                    // line 346
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["attribute"], "name", [], "any", false, false, false, 346);
                    yield "</td>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>";
                    // line 347
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["attribute"], "text", [], "any", false, false, false, 347);
                    yield "</td>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</tr>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['attribute'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 350
                yield "\t\t\t\t\t\t\t\t\t\t\t\t\t</tbody>
\t\t\t\t\t\t\t\t\t\t\t\t";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['attribute_group'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 352
            yield "\t\t\t\t\t\t\t\t\t\t\t</table>
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t";
        }
        // line 357
        yield "\t\t\t\t\t\t\t";
        if (($context["review_status"] ?? null)) {
            // line 358
            yield "\t\t\t\t\t\t\t\t<div class=\"accordion-item\">
\t\t\t\t\t\t\t\t\t<h2 class=\"accordion-header\">
\t\t\t\t\t\t\t\t\t\t<button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#productInfoSix\" aria-expanded=\"false\" aria-controls=\"productInfoSix\">
\t\t\t\t\t\t\t\t\t\t\t";
            // line 361
            yield ($context["tab_review"] ?? null);
            yield "
\t\t\t\t\t\t\t\t\t\t</button>
\t\t\t\t\t\t\t\t\t</h2>
\t\t\t\t\t\t\t\t\t<div id=\"productInfoSix\" class=\"accordion-collapse collapse\" data-bs-parent=\"#productInfoAccordion\">
\t\t\t\t\t\t\t\t\t\t<div class=\"accordion-body\">
\t\t\t\t\t\t\t\t\t\t\t";
            // line 366
            yield ($context["review"] ?? null);
            yield "
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t";
        }
        // line 371
        yield "\t\t\t\t\t\t</div>
\t\t\t\t\t</div>
\t\t\t\t</div>
\t\t\t</div>
\t\t\t";
        // line 375
        yield ($context["related"] ?? null);
        yield "
\t\t\t";
        // line 376
        yield ($context["content_bottom"] ?? null);
        yield "
\t\t</div>
\t\t";
        // line 378
        yield ($context["column_right"] ?? null);
        yield "
\t</div>
</div>
<script type=\"text/javascript\">
\$(document).ready(function () {
  // Subscription toggle
  \$('#input-subscription').on('change', function () {
    \$('.subscription').addClass('d-none');
    \$('#subscription-description-' + \$(this).val()).removeClass('d-none');
  });

  // Store original price data
  if (typeof window.originalBasePrice === 'undefined') {
    var originalPriceText = \$('.price-old').length ? \$('.price-old').text() : \$('.price-new').text();
    var baseMatch = originalPriceText.match(/(\\d[\\d.,]+)/);
    var currencyMatch = originalPriceText.match(/^[^\\d.,]+/);

    if (baseMatch) window.originalBasePrice = parseFloat(baseMatch[0].replace(/,/g, ''));
    if (currencyMatch) window.currencySymbol = currencyMatch[0]; else window.currencySymbol = '\$';

    if (\$('.price-old').length) {
      var specialText = \$('.price-new').text().trim();
      var specialMatch = specialText.match(/(\\d[\\d.,]+)/);
      if (specialMatch) window.originalSpecialPrice = parseFloat(specialMatch[0].replace(/,/g, ''));
    }
  }

  function calculateTotalPrice() {
    var base = window.originalBasePrice || 0;
    var special = window.originalSpecialPrice || 0;
    var symbol = window.currencySymbol || '\$';
    var current = special > 0 ? special : base;
    var additional = 0;

    // Add option prices (select)
    \$('select[name^=\"option\"]').each(function () {
      var sel = \$(this).find('option:selected');
      var match = sel.text().match(/\\(([+-])\\s*.*?([\\d,]+(?:\\.\\d+)?)\\)/);
      if (match) {
        var price = parseFloat(match[2].replace(/,/g, ''));
        additional += (match[1] === '+' ? 1 : -1) * price;
      }
    });

    // Add option prices (radio/checkbox)
    \$('input[type=\"radio\"][name^=\"option\"]:checked, input[type=\"checkbox\"][name^=\"option\"]:checked').each(function () {
      var label = \$('label[for=\"' + \$(this).attr('id') + '\"]');
      var match = label.text().match(/\\(([+-])\\s*.*?([\\d,]+(?:\\.\\d+)?)\\)/);
      if (match) {
        var price = parseFloat(match[2].replace(/,/g, ''));
        additional += (match[1] === '+' ? 1 : -1) * price;
      }
    });

    var total = current + additional;
    var formatted = symbol + total.toFixed(2).replace(/\\d(?=(\\d{3})+\\.)/g, '\$&,');
    \$('.price-new').html(formatted);
  }

  // Image swap on option change for radio buttons
  \$('input[type=\"radio\"][name^=\"option\"]').on('change', function () {
    var optionImage = \$(this).data('option-image');
    var popupImage = \$(this).data('option-image-popup');
    if (optionImage) {
      // Update the main product image
      \$('.image.magnific-popup a:first').attr('href', popupImage || optionImage);
      \$('.image.magnific-popup a:first img').attr('src', optionImage);
    }
    calculateTotalPrice();
  });

  // Image swap on option change for select dropdowns
  \$('select[name^=\"option\"]').on('change', function () {
    var selectedOption = \$(this).find('option:selected');
    var optionImage = selectedOption.data('option-image');
    var popupImage = selectedOption.data('option-image-popup');
    
    if (optionImage) {
      // Update the main product image
      \$('.image.magnific-popup a:first').attr('href', popupImage || optionImage);
      \$('.image.magnific-popup a:first img').attr('src', optionImage);
    }
    
    calculateTotalPrice();
  });

  // Other option change events
  \$('select[name^=\"option\"], input[type=\"checkbox\"][name^=\"option\"]').on('change', calculateTotalPrice);

  // Initial price calculation
  calculateTotalPrice();

  // Add to cart click
  \$('#button-cart').on('click', function (e) {
    e.preventDefault();
    \$('#form-product').submit();
  });

  // Add to cart submission with validation
  \$('#form-product').on('submit', function (e) {
    e.preventDefault();

    var form = \$(this);
    var button = \$('#button-cart');
    var hasError = false;

    form.find('.is-invalid').removeClass('is-invalid');
    form.find('.invalid-feedback').removeClass('d-block');

    form.find('.required').each(function () {
      var group = \$(this);
      var optionName = group.find('label').first().text().trim();
      var value = '';

      if (group.find('input[type=\"radio\"]').length) {
        value = group.find('input[type=\"radio\"]:checked').val();
      } else if (group.find('input[type=\"checkbox\"]').length) {
        value = group.find('input[type=\"checkbox\"]:checked').length > 0 ? '1' : '';
      } else if (group.find('select').length) {
        value = group.find('select').val();
      } else {
        value = group.find('input[type=\"text\"], input[type=\"number\"], textarea').val();
      }

      if (!value || value === '0') {
        group.find('.invalid-feedback').html('Please select ' + optionName).addClass('d-block');
        group.find('input, select, textarea').addClass('is-invalid');
        hasError = true;
      }
    });

    if (hasError) return false;

    \$.ajax({
      url: 'index.php?route=checkout/cart.add&language=";
        // line 512
        yield ($context["language"] ?? null);
        yield "',
      type: 'post',
      data: form.serialize(),
      dataType: 'json',
      beforeSend: function () {
        button.prop('disabled', true).html('<i class=\"fa fa-circle-notch fa-spin\"></i> Loading...');
      },
      complete: function () {
        button.prop('disabled', false).html('";
        // line 520
        yield ($context["button_cart"] ?? null);
        yield "');
      },
      success: function (json) {
        \$('#alert').find('.alert').remove();

        if (json.error) {
          if (json.error.warning) {
            \$('#alert').prepend('<div class=\"alert alert-danger alert-dismissible\"><i class=\"fa-solid fa-circle-exclamation\"></i> ' + json.error.warning + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');
          }

          for (var key in json.error) {
            if (key !== 'warning') {
              var element = \$('#input-option-' + key.split('_')[1]);
              if (element.length) {
                element.closest('.mb-3').addClass('is-invalid');
                element.closest('.mb-3').find('.invalid-feedback').html(json.error[key]).addClass('d-block');
              }
            }
          }
        }

        if (json.success) {
          \$('#alert').prepend('<div class=\"alert alert-success alert-dismissible\"><i class=\"fa-solid fa-circle-check\"></i> ' + json.success + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');
          \$('#cart').load('index.php?route=common/cart.info&language=";
        // line 543
        yield ($context["language"] ?? null);
        yield "', function () {
            \$('#cart').show();
            \$('[data-bs-toggle=\"tooltip\"]').tooltip();
          });
        }
      },
      error: function (xhr, ajaxOptions, thrownError) {
        var message = 'Error adding product to cart';
        try {
          var response = JSON.parse(xhr.responseText);
          if (response.error) {
            message = typeof response.error === 'object' ? Object.values(response.error).join(', ') : response.error;
          }
        } catch (e) {}

        \$('#alert').prepend('<div class=\"alert alert-danger alert-dismissible\"><i class=\"fa-solid fa-circle-exclamation\"></i> ' + message + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');
      }
    });
  });

\t// Product Enquiry
\t\$(\"#form-enquiry\").on(\"submit\", function (e) {
\t\te.preventDefault();

\t\t\$.ajax({
\t\t\turl: \"index.php?route=information/contact.send&language=";
        // line 568
        yield ($context["language"] ?? null);
        yield "\",
\t\t\ttype: \"post\",
\t\t\tdata: \$(\"#form-enquiry\").serialize(),
\t\t\tdataType: \"json\",
\t\t\tcontentType: 'application/x-www-form-urlencoded',
\t\t\tcache: false,
\t\t\tprocessData: false,
\t\t\tbeforeSend: function () {
\t\t\t\t\$(\"#form-enquiry button[type='submit']\").button(\"loading\");
\t\t\t},
\t\t\tcomplete: function () {
\t\t\t\t\$(\"#form-enquiry button[type='submit']\").button(\"reset\");
\t\t\t},
\t\t\tsuccess: function (json) {
\t\t\t\tconsole.log(json);
\t\t\t\t
\t\t\t\t\$(\".alert-dismissible\").remove();
\t\t\t\t\$(\"#form-enquiry\").find(\".is-invalid\").removeClass(\"is-invalid\");

\t\t\t\tif (json['error']) {
\t\t\t\t\t\tfor (key in json['error']) {
\t\t\t\t\t\t\t\t\$('#input-' + key.replaceAll('_', '-')).addClass('is-invalid').find('.form-control, .form-select, .form-check-input, .form-check-label').addClass('is-invalid');
\t\t\t\t\t\t\t\t\$('#error-' + key.replaceAll('_', '-')).html(json['error'][key]).addClass('d-block');
\t\t\t\t\t\t}
\t\t\t\t}

\t\t\t\tif (json[\"redirect\"]) {
\t\t\t\t\tlocation = json[\"redirect\"];
\t\t\t\t}
\t\t\t},
\t\t\terror: function (xhr, ajaxOptions, thrownError) {
\t\t\t\tconsole.log(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
\t\t\t},
\t\t});
\t});

  // Magnific popup init
  \$('.magnific-popup').magnificPopup({
    type: 'image',
    delegate: 'a',
    gallery: {
      enabled: true
    }
  });
});
</script>

<!-- Product Image Zoom -->

\t<script src=\"catalog/view/javascript/jquery/jquery.ez-plus.js\">
\t</script>

\t<script type=\"text/javascript\">
\tif (\$(window).width() > 769) {
\$(\"#zoom_01\").ezPlus({
gallery: 'gallery',
galleryActiveClass: 'active',
borderSize: 1,
showLens: true,
borderColour: \"#ddd\",
lensSize: 50,
lensBorderSize: 1,
lensOpacity: 0.1,
lensBorderColour: \"#333\",
zoomWindowOffsetX: 15
});
}
\t</script>
\t";
        // line 636
        yield ($context["footer"] ?? null);
        yield "
";
        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "catalog/view/template/product/product.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  1382 => 636,  1311 => 568,  1283 => 543,  1257 => 520,  1246 => 512,  1109 => 378,  1104 => 376,  1100 => 375,  1094 => 371,  1086 => 366,  1078 => 361,  1073 => 358,  1070 => 357,  1063 => 352,  1056 => 350,  1047 => 347,  1043 => 346,  1040 => 345,  1036 => 344,  1028 => 339,  1023 => 336,  1019 => 335,  1010 => 329,  1005 => 326,  1003 => 325,  996 => 321,  988 => 316,  981 => 311,  977 => 308,  974 => 306,  967 => 302,  961 => 299,  952 => 293,  944 => 288,  939 => 286,  931 => 281,  926 => 279,  921 => 277,  917 => 276,  912 => 274,  907 => 271,  902 => 268,  897 => 266,  893 => 264,  891 => 263,  885 => 260,  880 => 258,  876 => 257,  872 => 256,  868 => 254,  863 => 251,  852 => 249,  848 => 248,  845 => 247,  834 => 245,  830 => 244,  826 => 243,  820 => 240,  817 => 239,  814 => 238,  810 => 236,  804 => 235,  798 => 232,  790 => 231,  784 => 230,  777 => 229,  775 => 228,  772 => 227,  766 => 224,  758 => 223,  752 => 222,  745 => 221,  743 => 220,  740 => 219,  734 => 216,  726 => 215,  720 => 214,  713 => 213,  711 => 212,  708 => 211,  702 => 208,  695 => 206,  691 => 205,  678 => 203,  671 => 201,  664 => 200,  662 => 199,  659 => 198,  653 => 195,  643 => 194,  637 => 193,  630 => 192,  628 => 191,  625 => 190,  619 => 187,  609 => 186,  603 => 185,  596 => 184,  594 => 183,  591 => 182,  585 => 179,  582 => 178,  574 => 175,  567 => 173,  565 => 172,  560 => 171,  546 => 169,  544 => 168,  540 => 167,  532 => 166,  529 => 165,  525 => 164,  521 => 163,  517 => 162,  510 => 161,  508 => 160,  505 => 159,  499 => 156,  496 => 155,  488 => 152,  481 => 150,  479 => 149,  474 => 148,  460 => 146,  458 => 145,  454 => 144,  451 => 143,  446 => 141,  441 => 140,  439 => 139,  434 => 137,  430 => 136,  426 => 135,  422 => 133,  418 => 132,  414 => 131,  410 => 130,  403 => 129,  401 => 128,  398 => 127,  392 => 124,  389 => 123,  382 => 121,  375 => 119,  373 => 118,  369 => 117,  366 => 116,  361 => 115,  356 => 114,  354 => 113,  349 => 112,  345 => 111,  341 => 110,  335 => 109,  329 => 108,  322 => 107,  319 => 106,  315 => 105,  310 => 103,  307 => 102,  305 => 101,  301 => 99,  299 => 98,  290 => 92,  281 => 88,  273 => 85,  267 => 81,  263 => 79,  260 => 78,  249 => 76,  245 => 75,  240 => 72,  237 => 71,  232 => 69,  227 => 68,  224 => 67,  219 => 65,  214 => 64,  211 => 63,  204 => 59,  200 => 58,  196 => 56,  189 => 52,  185 => 50,  183 => 49,  180 => 48,  178 => 47,  170 => 42,  166 => 41,  160 => 38,  155 => 36,  152 => 35,  146 => 33,  144 => 32,  140 => 31,  137 => 30,  132 => 27,  128 => 25,  111 => 23,  107 => 22,  104 => 21,  101 => 20,  87 => 18,  85 => 17,  81 => 15,  79 => 14,  74 => 12,  69 => 10,  66 => 9,  55 => 6,  52 => 5,  48 => 4,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{{ header }}
<div id=\"product-info\" class=\"container\">
\t<ul class=\"breadcrumb\">
\t\t{% for breadcrumb in breadcrumbs %}
\t\t\t<li class=\"breadcrumb-item\">
\t\t\t\t<a href=\"{{ breadcrumb.href }}\">{{ breadcrumb.text }}</a>
\t\t\t</li>
\t\t{% endfor %}
\t</ul>
\t<div class=\"row\">{{ column_left }}
\t\t<div id=\"content\" class=\"col\">
\t\t\t{{ content_top }}
\t\t\t<div class=\"row mb-3\">
\t\t\t\t{% if thumb or images %}
          <div class=\"col-sm\">
            <div class=\"image magnific-popup\">
              {% if thumb %}
                <a href=\"{{ popup }}\" title=\"{{ heading_title }}\"><img src=\"{{ thumb }}\" title=\"{{ heading_title }}\" alt=\"{{ heading_title }}\" class=\"img-thumbnail mb-3\"/></a>
              {% endif %}
              {% if images %}
                <div class=\"thumbnails\">
                  {% for image in images %}
                    <a href=\"{{ image.popup }}\" title=\"{{ heading_title }}\"><img src=\"{{ image.thumb }}\" title=\"{{ heading_title }}\" alt=\"{{ heading_title }}\" class=\"img-thumbnail\"/></a>&nbsp;
                  {% endfor %}
                </div>
              {% endif %}
            </div>
          </div>
        {% endif %}
\t\t\t\t<div class=\"col-md-7\">
\t\t\t\t\t<h1>{{ heading_title }}</h1>
\t\t\t\t\t{% if additional_info1 %}
\t\t\t\t\t\t{{ additional_info1 }}
\t\t\t\t\t{% endif %}
\t\t\t\t\t<ul class=\"list-unstyled\">
\t\t\t\t\t\t<li>{{ text_model }}
\t\t\t\t\t\t\t<span class=\"primary\">
\t\t\t\t\t\t\t\t<strong>{{ model }}</strong>
\t\t\t\t\t\t\t</span>
\t\t\t\t\t\t</li>
\t\t\t\t\t\t<li>{{ text_stock }}
\t\t\t\t\t\t\t<span class=\"green\">{{ stock }}</span>
\t\t\t\t\t\t</li>
\t\t\t\t\t</ul>
\t\t\t\t\t<div class=\"row\">
\t\t\t\t\t\t<div class=\"col-md-5\">
\t\t\t\t\t\t\t{% if price %}
\t\t\t\t\t\t\t\t<ul class=\"list-unstyled\">
\t\t\t\t\t\t\t\t\t{% if not special %}
\t\t\t\t\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t\t\t\t\t<h2>
\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"price-new\">{{ price }}</span>
\t\t\t\t\t\t\t\t\t\t\t</h2>
\t\t\t\t\t\t\t\t\t\t</li>
\t\t\t\t\t\t\t\t\t{% else %}
\t\t\t\t\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t\t\t\t\t<h2>
\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"price-old\">{{ price }}</span>
\t\t\t\t\t\t\t\t\t\t\t\t<span class=\"price-new\">{{ special }}</span>
\t\t\t\t\t\t\t\t\t\t\t</h2>
\t\t\t\t\t\t\t\t\t\t</li>
\t\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t\t\t{% if tax %}
\t\t\t\t\t\t\t\t\t\t<li>{{ text_tax }}
\t\t\t\t\t\t\t\t\t\t\t{{ tax }}</li>
\t\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t\t\t{% if points %}
\t\t\t\t\t\t\t\t\t\t<li>{{ text_points }}
\t\t\t\t\t\t\t\t\t\t\t{{ points }}</li>
\t\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t\t\t{% if discounts %}
\t\t\t\t\t\t\t\t\t\t<li>
\t\t\t\t\t\t\t\t\t\t\t<hr>
\t\t\t\t\t\t\t\t\t\t</li>
\t\t\t\t\t\t\t\t\t\t{% for discount in discounts %}
\t\t\t\t\t\t\t\t\t\t\t<li>{{ discount.quantity }}{{ text_discount }}{{ discount.price }}</li>
\t\t\t\t\t\t\t\t\t\t{% endfor %}
\t\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t\t</ul>
\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t</div>
\t\t\t\t\t\t<div class=\"col-md-7 ps-4\">
\t\t\t\t\t\t\t<form method=\"post\" data-oc-toggle=\"ajax\">
\t\t\t\t\t\t\t\t<div class=\"btn-group\">
\t\t\t\t\t\t\t\t\t<button type=\"submit\" formaction=\"{{ wishlist_add }}\" data-bs-toggle=\"tooltip\" class=\"btn btn-light btn-md\" title=\"{{ button_wishlist }}\">
\t\t\t\t\t\t\t\t\t\t<i class=\"fa-solid fa-heart\"></i>
\t\t\t\t\t\t\t\t\t</button>
\t\t\t\t\t\t\t\t\t<button type=\"submit\" formaction=\"{{ compare_add }}\" data-bs-toggle=\"tooltip\" class=\"btn btn-light btn-md\" title=\"{{ button_compare }}\">
\t\t\t\t\t\t\t\t\t\t<i class=\"fa-solid fa-arrow-right-arrow-left\"></i>
\t\t\t\t\t\t\t\t\t</button>
\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t<input type=\"hidden\" name=\"product_id\" value=\"{{ product_id }}\"/>
\t\t\t\t\t\t\t</form>
\t\t\t\t\t\t</div>
\t\t\t\t\t</div>
\t\t\t\t\t<div class=\"row\">
\t\t\t\t\t\t<div class=\"col-md-7\">
\t\t\t\t\t\t\t{% if stock == \"In Stock\" %}
\t\t\t\t\t\t\t\t<div id=\"product col-md-6\">
\t\t\t\t\t\t\t\t\t<form id=\"form-product\">
\t\t\t\t\t\t\t\t\t\t{% if options %}
\t\t\t\t\t\t\t\t\t\t\t<hr>
\t\t\t\t\t\t\t\t\t\t\t<h3>{{ text_option }}</h3>
\t\t\t\t\t\t\t\t\t\t\t<div class=\"prodoptions\">
\t\t\t\t\t\t\t\t\t\t\t\t{% for option in options %}
\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option.type == 'select' %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3{% if option.required %} required{% endif %}\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-{{ option.product_option_id }}\" class=\"form-label\">{{ option.name }}</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<select name=\"option[{{ option.product_option_id }}]\" id=\"input-option-{{ option.product_option_id }}\" class=\"form-select\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value=\"\">{{ text_select }}</option>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% for option_value in option.product_option_value %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value=\"{{ option_value.product_option_value_id }}\" 
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option_value.image %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata-option-image=\"{{ option_value.image }}\"
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata-option-image-popup=\"{{ option_value.popup_image|default(option_value.image) }}\"
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ option_value.name }}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option_value.price %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t({{ option_value.price_prefix }}{{ option_value.price }})
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</option>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% endfor %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</select>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-{{ option.product_option_id }}\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}

\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option.type == 'radio' %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3{% if option.required %} required{% endif %}\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label class=\"form-label\">{{ option.name }}</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"input-option-{{ option.product_option_id }}\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% for option_value in option.product_option_value %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"form-check option-image\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"radio\" 
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tname=\"option[{{ option.product_option_id }}]\" 
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue=\"{{ option_value.product_option_value_id }}\" 
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"input-option-value-{{ option_value.product_option_value_id }}\" 
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"form-check-input\"
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option_value.image %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata-option-image=\"{{ option_value.image }}\"
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata-option-image-popup=\"{{ option_value.popup_image|default(option_value.image) }}\"
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-value-{{ option_value.product_option_value_id }}\" class=\"form-check-label\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option_value.image %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img src=\"{{ option_value.image }}\" alt=\"{{ option_value.name }} {% if option_value.price %}{{ option_value.price_prefix }} {{ option_value.price }}{% endif %}\" class=\"img-thumbnail\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ option_value.name }}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option_value.price %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t({{ option_value.price_prefix }}{{ option_value.price }})
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% endfor %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-{{ option.product_option_id }}\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}

\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option.type == 'checkbox' %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3{% if option.required %} required{% endif %}\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label class=\"form-label\">{{ option.name }}</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"input-option-{{ option.product_option_id }}\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% for option_value in option.product_option_value %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"form-check option-image\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"checkbox\" name=\"option[{{ option.product_option_id }}][]\" value=\"{{ option_value.product_option_value_id }}\" id=\"input-option-value-{{ option_value.product_option_value_id }}\" class=\"form-check-input\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-value-{{ option_value.product_option_value_id }}\" class=\"form-check-label\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option_value.image %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img src=\"{{ option_value.image }}\" alt=\"{{ option_value.name }} {% if option_value.price %}{{ option_value.price_prefix }} {{ option_value.price }}{% endif %}\" class=\"img-thumbnail\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ option_value.name }}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option_value.price %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t({{ option_value.price_prefix }}{{ option_value.price }})
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% endfor %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-{{ option.product_option_id }}\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}

\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option.type == 'text' %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3{% if option.required %} required{% endif %}\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-{{ option.product_option_id }}\" class=\"form-label\">{{ option.name }}</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" name=\"option[{{ option.product_option_id }}]\" value=\"{{ option.value }}\" placeholder=\"{{ option.name }}\" id=\"input-option-{{ option.product_option_id }}\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-{{ option.product_option_id }}\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}

\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option.type == 'textarea' %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3{% if option.required %} required{% endif %}\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-{{ option.product_option_id }}\" class=\"form-label\">{{ option.name }}</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<textarea name=\"option[{{ option.product_option_id }}]\" rows=\"5\" placeholder=\"{{ option.name }}\" id=\"input-option-{{ option.product_option_id }}\" class=\"form-control\">{{ option.value }}</textarea>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-{{ option.product_option_id }}\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}

\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option.type == 'file' %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3{% if option.required %} required{% endif %}\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"button-upload-{{ option.product_option_id }}\" class=\"form-label\">{{ option.name }}</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<button type=\"button\" id=\"button-upload-{{ option.product_option_id }}\" data-oc-toggle=\"upload\" data-oc-url=\"{{ upload }}\" data-oc-target=\"#input-option-{{ option.product_option_id }}\" data-oc-size-max=\"{{ config_file_max_size }}\" data-oc-size-error=\"{{ error_upload_size }}\" class=\"btn btn-light btn-block\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<i class=\"fa-solid fa-upload\"></i>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ button_upload }}</button>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"hidden\" name=\"option[{{ option.product_option_id }}]\" value=\"\" id=\"input-option-{{ option.product_option_id }}\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-{{ option.product_option_id }}\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}

\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option.type == 'date' %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3{% if option.required %} required{% endif %}\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-{{ option.product_option_id }}\" class=\"form-label\">{{ option.name }}</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"date\" name=\"option[{{ option.product_option_id }}]\" value=\"{{ option.value }}\" id=\"input-option-{{ option.product_option_id }}\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-{{ option.product_option_id }}\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}

\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option.type == 'time' %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3{% if option.required %} required{% endif %}\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-{{ option.product_option_id }}\" class=\"form-label\">{{ option.name }}</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"time\" name=\"option[{{ option.product_option_id }}]\" value=\"{{ option.value }}\" id=\"input-option-{{ option.product_option_id }}\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-{{ option.product_option_id }}\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}

\t\t\t\t\t\t\t\t\t\t\t\t\t{% if option.type == 'datetime' %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3{% if option.required %} required{% endif %}\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-option-{{ option.product_option_id }}\" class=\"form-label\">{{ option.name }}</label>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"datetime-local\" name=\"option[{{ option.product_option_id }}]\" value=\"{{ option.value }}\" id=\"input-option-{{ option.product_option_id }}\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-option-{{ option.product_option_id }}\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t\t\t\t\t\t{% endfor %}
\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t\t\t\t{% if subscription_plans %}
\t\t\t\t\t\t\t\t\t\t\t<hr/>
\t\t\t\t\t\t\t\t\t\t\t<h3>{{ text_subscription }}</h3>
\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3 required\">
\t\t\t\t\t\t\t\t\t\t\t\t<select name=\"subscription_plan_id\" id=\"input-subscription\" class=\"form-select\">
\t\t\t\t\t\t\t\t\t\t\t\t\t<option value=\"\">{{ text_select }}</option>
\t\t\t\t\t\t\t\t\t\t\t\t\t{% for subscription_plan in subscription_plans %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option value=\"{{ subscription_plan.subscription_plan_id }}\">{{ subscription_plan.name }}</option>
\t\t\t\t\t\t\t\t\t\t\t\t\t{% endfor %}
\t\t\t\t\t\t\t\t\t\t\t\t</select>
\t\t\t\t\t\t\t\t\t\t\t\t{% for subscription_plan in subscription_plans %}
\t\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"subscription-description-{{ subscription_plan.subscription_plan_id }}\" class=\"form-text subscription d-none\">{{ subscription_plan.description }}</div>
\t\t\t\t\t\t\t\t\t\t\t\t{% endfor %}
\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-subscription\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t\t\t\t<div class=\"mb-3 qtybox\">
\t\t\t\t\t\t\t\t\t\t\t<div class=\"input-group\">
\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"input-group-text\">{{ entry_qty }}</div>
\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"number\" name=\"quantity\" value=\"{{ minimum }}\" size=\"2\" id=\"input-quantity\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t\t\t<button type=\"submit\" id=\"button-cart\" class=\"btn btn-primary btn-md btn-block\">{{ button_cart }}</button>
\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t\t<input type=\"hidden\" name=\"product_id\" value=\"{{ product_id }}\" id=\"input-product-id\"/>
\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-quantity\" class=\"form-text\"></div>
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t{% if minimum > 1 %}
\t\t\t\t\t\t\t\t\t\t\t<div class=\"alert alert-warning\">
\t\t\t\t\t\t\t\t\t\t\t\t<i class=\"fa-solid fa-circle-info\"></i>
\t\t\t\t\t\t\t\t\t\t\t\t{{ text_minimum }}</div>
\t\t\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t\t\t</form>
\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t{% else %}
\t\t\t\t\t\t\t\t<p class=\"mt-3\"><span class=\"text-danger\"><strong>Product Currently Out of Stock</strong></span></p>
\t\t\t\t\t\t\t\t<p>Please use enquiry form below if interested.</p>
\t\t\t\t\t\t\t\t<form id=\"form-enquiry\" class=\"product-enquiry\">
\t\t\t\t\t\t\t\t\t<h3>Enquiry for: {{ heading_title }}</h3>
\t\t\t\t\t\t\t\t\t<fieldset>
\t\t\t\t\t\t\t\t\t\t<legend>{{ text_contact }}</legend>
\t\t\t\t\t\t\t\t\t\t<input type=\"hidden\" name=\"emailsub\" value=\"{{ heading_title }}\" id=\"input-emailsub\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t<div class=\"row mb-3 required\">
\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-name\" class=\"col-sm-3 col-form-label\">{{ entry_name }}</label>
\t\t\t\t\t\t\t\t\t\t\t<div class=\"col-sm-9\">
\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" name=\"name\" value=\"{{ name }}\" id=\"input-name\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-name\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t<div class=\"row mb-3 required\">
\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-email\" class=\"col-sm-3 col-form-label\">{{ entry_email }}</label>
\t\t\t\t\t\t\t\t\t\t\t<div class=\"col-sm-9\">
\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" name=\"email\" value=\"{{ email }}\" id=\"input-email\" class=\"form-control\"/>
\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-email\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t<div class=\"row mb-3 required\">
\t\t\t\t\t\t\t\t\t\t\t<label for=\"input-enquiry\" class=\"col-sm-3 col-form-label\">{{ entry_enquiry }}</label>
\t\t\t\t\t\t\t\t\t\t\t<div class=\"col-sm-9\">
\t\t\t\t\t\t\t\t\t\t\t\t<textarea name=\"enquiry\" rows=\"2\" id=\"input-enquiry\" class=\"form-control\"></textarea>
\t\t\t\t\t\t\t\t\t\t\t\t<div id=\"error-enquiry\" class=\"invalid-feedback\"></div>
\t\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t\t{{ captcha }}
\t\t\t\t\t\t\t\t\t</fieldset>
\t\t\t\t\t\t\t\t\t<div class=\"text-end\">
\t\t\t\t\t\t\t\t\t\t<button type=\"submit\" class=\"btn btn-primary\">{{ button_enquiry }}</button>
\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t</form>
\t\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t</div>
\t\t\t\t\t\t{# empty col space #}
\t\t\t\t\t</div>
\t\t\t\t\t<hr>
\t\t\t\t\t{# Product Info Accordion #}
\t\t\t\t\t<div class=\"prodinfo\">
\t\t\t\t\t\t<div class=\"accordion\" id=\"productInfoAccordion\">
\t\t\t\t\t\t\t<div class=\"accordion-item\">
\t\t\t\t\t\t\t\t<h2 class=\"accordion-header\">
\t\t\t\t\t\t\t\t\t<button class=\"accordion-button\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#productInfoOne\" aria-expanded=\"true\" aria-controls=\"productInfoOne\">
\t\t\t\t\t\t\t\t\t\t{{ tab_description }}
\t\t\t\t\t\t\t\t\t</button>
\t\t\t\t\t\t\t\t</h2>
\t\t\t\t\t\t\t\t<div id=\"productInfoOne\" class=\"accordion-collapse collapse show\" data-bs-parent=\"#productInfoAccordion\">
\t\t\t\t\t\t\t\t\t<div class=\"accordion-body\">
\t\t\t\t\t\t\t\t\t\t{{ description }}
\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t{% if attribute_groups %}
\t\t\t\t\t\t\t\t<div class=\"accordion-item\">
\t\t\t\t\t\t\t\t\t<h2 class=\"accordion-header\">
\t\t\t\t\t\t\t\t\t\t<button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#productInfoFive\" aria-expanded=\"false\" aria-controls=\"productInfoFive\">
\t\t\t\t\t\t\t\t\t\t\t{{ tab_attribute }}
\t\t\t\t\t\t\t\t\t\t</button>
\t\t\t\t\t\t\t\t\t</h2>
\t\t\t\t\t\t\t\t\t<div id=\"productInfoFive\" class=\"accordion-collapse collapse\" data-bs-parent=\"#productInfoAccordion\">
\t\t\t\t\t\t\t\t\t\t<div class=\"accordion-body\">
\t\t\t\t\t\t\t\t\t\t\t<table class=\"table table-bordered\">
\t\t\t\t\t\t\t\t\t\t\t\t{% for attribute_group in attribute_groups %}
\t\t\t\t\t\t\t\t\t\t\t\t\t<thead>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t<tr>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td colspan=\"2\">
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<strong>{{ attribute_group.name }}</strong>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</td>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t</tr>
\t\t\t\t\t\t\t\t\t\t\t\t\t</thead>
\t\t\t\t\t\t\t\t\t\t\t\t\t<tbody>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% for attribute in attribute_group.attribute %}
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<tr>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>{{ attribute.name }}</td>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<td>{{ attribute.text }}</td>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</tr>
\t\t\t\t\t\t\t\t\t\t\t\t\t\t{% endfor %}
\t\t\t\t\t\t\t\t\t\t\t\t\t</tbody>
\t\t\t\t\t\t\t\t\t\t\t\t{% endfor %}
\t\t\t\t\t\t\t\t\t\t\t</table>
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t\t{% if review_status %}
\t\t\t\t\t\t\t\t<div class=\"accordion-item\">
\t\t\t\t\t\t\t\t\t<h2 class=\"accordion-header\">
\t\t\t\t\t\t\t\t\t\t<button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#productInfoSix\" aria-expanded=\"false\" aria-controls=\"productInfoSix\">
\t\t\t\t\t\t\t\t\t\t\t{{ tab_review }}
\t\t\t\t\t\t\t\t\t\t</button>
\t\t\t\t\t\t\t\t\t</h2>
\t\t\t\t\t\t\t\t\t<div id=\"productInfoSix\" class=\"accordion-collapse collapse\" data-bs-parent=\"#productInfoAccordion\">
\t\t\t\t\t\t\t\t\t\t<div class=\"accordion-body\">
\t\t\t\t\t\t\t\t\t\t\t{{ review }}
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t{% endif %}
\t\t\t\t\t\t</div>
\t\t\t\t\t</div>
\t\t\t\t</div>
\t\t\t</div>
\t\t\t{{ related }}
\t\t\t{{ content_bottom }}
\t\t</div>
\t\t{{ column_right }}
\t</div>
</div>
<script type=\"text/javascript\">
\$(document).ready(function () {
  // Subscription toggle
  \$('#input-subscription').on('change', function () {
    \$('.subscription').addClass('d-none');
    \$('#subscription-description-' + \$(this).val()).removeClass('d-none');
  });

  // Store original price data
  if (typeof window.originalBasePrice === 'undefined') {
    var originalPriceText = \$('.price-old').length ? \$('.price-old').text() : \$('.price-new').text();
    var baseMatch = originalPriceText.match(/(\\d[\\d.,]+)/);
    var currencyMatch = originalPriceText.match(/^[^\\d.,]+/);

    if (baseMatch) window.originalBasePrice = parseFloat(baseMatch[0].replace(/,/g, ''));
    if (currencyMatch) window.currencySymbol = currencyMatch[0]; else window.currencySymbol = '\$';

    if (\$('.price-old').length) {
      var specialText = \$('.price-new').text().trim();
      var specialMatch = specialText.match(/(\\d[\\d.,]+)/);
      if (specialMatch) window.originalSpecialPrice = parseFloat(specialMatch[0].replace(/,/g, ''));
    }
  }

  function calculateTotalPrice() {
    var base = window.originalBasePrice || 0;
    var special = window.originalSpecialPrice || 0;
    var symbol = window.currencySymbol || '\$';
    var current = special > 0 ? special : base;
    var additional = 0;

    // Add option prices (select)
    \$('select[name^=\"option\"]').each(function () {
      var sel = \$(this).find('option:selected');
      var match = sel.text().match(/\\(([+-])\\s*.*?([\\d,]+(?:\\.\\d+)?)\\)/);
      if (match) {
        var price = parseFloat(match[2].replace(/,/g, ''));
        additional += (match[1] === '+' ? 1 : -1) * price;
      }
    });

    // Add option prices (radio/checkbox)
    \$('input[type=\"radio\"][name^=\"option\"]:checked, input[type=\"checkbox\"][name^=\"option\"]:checked').each(function () {
      var label = \$('label[for=\"' + \$(this).attr('id') + '\"]');
      var match = label.text().match(/\\(([+-])\\s*.*?([\\d,]+(?:\\.\\d+)?)\\)/);
      if (match) {
        var price = parseFloat(match[2].replace(/,/g, ''));
        additional += (match[1] === '+' ? 1 : -1) * price;
      }
    });

    var total = current + additional;
    var formatted = symbol + total.toFixed(2).replace(/\\d(?=(\\d{3})+\\.)/g, '\$&,');
    \$('.price-new').html(formatted);
  }

  // Image swap on option change for radio buttons
  \$('input[type=\"radio\"][name^=\"option\"]').on('change', function () {
    var optionImage = \$(this).data('option-image');
    var popupImage = \$(this).data('option-image-popup');
    if (optionImage) {
      // Update the main product image
      \$('.image.magnific-popup a:first').attr('href', popupImage || optionImage);
      \$('.image.magnific-popup a:first img').attr('src', optionImage);
    }
    calculateTotalPrice();
  });

  // Image swap on option change for select dropdowns
  \$('select[name^=\"option\"]').on('change', function () {
    var selectedOption = \$(this).find('option:selected');
    var optionImage = selectedOption.data('option-image');
    var popupImage = selectedOption.data('option-image-popup');
    
    if (optionImage) {
      // Update the main product image
      \$('.image.magnific-popup a:first').attr('href', popupImage || optionImage);
      \$('.image.magnific-popup a:first img').attr('src', optionImage);
    }
    
    calculateTotalPrice();
  });

  // Other option change events
  \$('select[name^=\"option\"], input[type=\"checkbox\"][name^=\"option\"]').on('change', calculateTotalPrice);

  // Initial price calculation
  calculateTotalPrice();

  // Add to cart click
  \$('#button-cart').on('click', function (e) {
    e.preventDefault();
    \$('#form-product').submit();
  });

  // Add to cart submission with validation
  \$('#form-product').on('submit', function (e) {
    e.preventDefault();

    var form = \$(this);
    var button = \$('#button-cart');
    var hasError = false;

    form.find('.is-invalid').removeClass('is-invalid');
    form.find('.invalid-feedback').removeClass('d-block');

    form.find('.required').each(function () {
      var group = \$(this);
      var optionName = group.find('label').first().text().trim();
      var value = '';

      if (group.find('input[type=\"radio\"]').length) {
        value = group.find('input[type=\"radio\"]:checked').val();
      } else if (group.find('input[type=\"checkbox\"]').length) {
        value = group.find('input[type=\"checkbox\"]:checked').length > 0 ? '1' : '';
      } else if (group.find('select').length) {
        value = group.find('select').val();
      } else {
        value = group.find('input[type=\"text\"], input[type=\"number\"], textarea').val();
      }

      if (!value || value === '0') {
        group.find('.invalid-feedback').html('Please select ' + optionName).addClass('d-block');
        group.find('input, select, textarea').addClass('is-invalid');
        hasError = true;
      }
    });

    if (hasError) return false;

    \$.ajax({
      url: 'index.php?route=checkout/cart.add&language={{ language }}',
      type: 'post',
      data: form.serialize(),
      dataType: 'json',
      beforeSend: function () {
        button.prop('disabled', true).html('<i class=\"fa fa-circle-notch fa-spin\"></i> Loading...');
      },
      complete: function () {
        button.prop('disabled', false).html('{{ button_cart }}');
      },
      success: function (json) {
        \$('#alert').find('.alert').remove();

        if (json.error) {
          if (json.error.warning) {
            \$('#alert').prepend('<div class=\"alert alert-danger alert-dismissible\"><i class=\"fa-solid fa-circle-exclamation\"></i> ' + json.error.warning + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');
          }

          for (var key in json.error) {
            if (key !== 'warning') {
              var element = \$('#input-option-' + key.split('_')[1]);
              if (element.length) {
                element.closest('.mb-3').addClass('is-invalid');
                element.closest('.mb-3').find('.invalid-feedback').html(json.error[key]).addClass('d-block');
              }
            }
          }
        }

        if (json.success) {
          \$('#alert').prepend('<div class=\"alert alert-success alert-dismissible\"><i class=\"fa-solid fa-circle-check\"></i> ' + json.success + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');
          \$('#cart').load('index.php?route=common/cart.info&language={{ language }}', function () {
            \$('#cart').show();
            \$('[data-bs-toggle=\"tooltip\"]').tooltip();
          });
        }
      },
      error: function (xhr, ajaxOptions, thrownError) {
        var message = 'Error adding product to cart';
        try {
          var response = JSON.parse(xhr.responseText);
          if (response.error) {
            message = typeof response.error === 'object' ? Object.values(response.error).join(', ') : response.error;
          }
        } catch (e) {}

        \$('#alert').prepend('<div class=\"alert alert-danger alert-dismissible\"><i class=\"fa-solid fa-circle-exclamation\"></i> ' + message + ' <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button></div>');
      }
    });
  });

\t// Product Enquiry
\t\$(\"#form-enquiry\").on(\"submit\", function (e) {
\t\te.preventDefault();

\t\t\$.ajax({
\t\t\turl: \"index.php?route=information/contact.send&language={{ language }}\",
\t\t\ttype: \"post\",
\t\t\tdata: \$(\"#form-enquiry\").serialize(),
\t\t\tdataType: \"json\",
\t\t\tcontentType: 'application/x-www-form-urlencoded',
\t\t\tcache: false,
\t\t\tprocessData: false,
\t\t\tbeforeSend: function () {
\t\t\t\t\$(\"#form-enquiry button[type='submit']\").button(\"loading\");
\t\t\t},
\t\t\tcomplete: function () {
\t\t\t\t\$(\"#form-enquiry button[type='submit']\").button(\"reset\");
\t\t\t},
\t\t\tsuccess: function (json) {
\t\t\t\tconsole.log(json);
\t\t\t\t
\t\t\t\t\$(\".alert-dismissible\").remove();
\t\t\t\t\$(\"#form-enquiry\").find(\".is-invalid\").removeClass(\"is-invalid\");

\t\t\t\tif (json['error']) {
\t\t\t\t\t\tfor (key in json['error']) {
\t\t\t\t\t\t\t\t\$('#input-' + key.replaceAll('_', '-')).addClass('is-invalid').find('.form-control, .form-select, .form-check-input, .form-check-label').addClass('is-invalid');
\t\t\t\t\t\t\t\t\$('#error-' + key.replaceAll('_', '-')).html(json['error'][key]).addClass('d-block');
\t\t\t\t\t\t}
\t\t\t\t}

\t\t\t\tif (json[\"redirect\"]) {
\t\t\t\t\tlocation = json[\"redirect\"];
\t\t\t\t}
\t\t\t},
\t\t\terror: function (xhr, ajaxOptions, thrownError) {
\t\t\t\tconsole.log(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
\t\t\t},
\t\t});
\t});

  // Magnific popup init
  \$('.magnific-popup').magnificPopup({
    type: 'image',
    delegate: 'a',
    gallery: {
      enabled: true
    }
  });
});
</script>

<!-- Product Image Zoom -->

\t<script src=\"catalog/view/javascript/jquery/jquery.ez-plus.js\">
\t</script>

\t<script type=\"text/javascript\">
\tif (\$(window).width() > 769) {
\$(\"#zoom_01\").ezPlus({
gallery: 'gallery',
galleryActiveClass: 'active',
borderSize: 1,
showLens: true,
borderColour: \"#ddd\",
lensSize: 50,
lensBorderSize: 1,
lensOpacity: 0.1,
lensBorderColour: \"#333\",
zoomWindowOffsetX: 15
});
}
\t</script>
\t{{ footer }}
", "catalog/view/template/product/product.twig", "D:\\wamp64\\www\\pex\\pondexpo\\catalog\\view\\template\\product\\product.twig");
    }
}
