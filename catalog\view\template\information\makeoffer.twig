{{ header }}
<div id="information-makeoffer" class="container">
	<ul class="breadcrumb">
		{% for breadcrumb in breadcrumbs %}
			<li class="breadcrumb-item">
				<a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a>
			</li>
		{% endfor %}
	</ul>
	<div class="row">{{ column_left }}
		<div id="content" class="col">{{ content_top }}
			<h1>{{ heading_title }}</h1>
			<div class="row">
				<div class="col-md-8">
					<p>{{ text_description }}</p>
				</div>
				<div class="col-md-4 makeofferform">

					<form id="form-makeoffer" action="{{ send }}" method="post" data-oc-toggle="ajax">
						<fieldset>
							<h3>{{ form_title }}</h3>
							<div class="row mb-3 required">
								<label for="input-itemdescription" class="form-label">{{ entry_itemdescription }}</label>
								<input type="text" name="itemdescription" value="" id="input-itemdescription" class="form-control"/>
								<div id="error-itemdescription" class="invalid-feedback"></div>
							</div>
							<div class="row mb-3 required">
								<label for="input-currentprice" class="form-label">{{ entry_currentprice }}</label>
								<input type="number" name="currentprice" value="{{ currentprice }}" id="input-currentprice" class="form-control"/>
								<div id="error-currentprice" class="invalid-feedback"></div>
							</div>
							<div class="row mb-3 required">
								<label for="input-offerprice" class="form-label">{{ entry_offerprice }}</label>
								<input type="number" name="offerprice" value="" id="input-offerprice" class="form-control" step="0.01" min="0.01"/>
								<div id="error-offerprice" class="invalid-feedback"></div>
							</div>
							<div class="row mb-3 required">
								<label for="input-zip" class="form-label">{{ entry_ziptoship }}</label>
								<input type="number" name="ziptoship" value="" id="input-ziptoship" class="form-control" />
								<div id="error-ziptoship" class="invalid-feedback"></div>
							</div>
							<div class="row mb-3 required">
								<label for="input-name" class="form-label">{{ entry_name }}</label>
								<input type="text" name="name" value="{{ name }}" id="input-name" class="form-control"/>
								<div id="error-name" class="invalid-feedback"></div>
							</div>
							<div class="row mb-3 required">
								<label for="input-email" class="form-label">{{ entry_email }}</label>
								<input type="text" name="email" value="{{ email }}" id="input-email" class="form-control"/>
								<div id="error-email" class="invalid-feedback"></div>
							</div>
							<div class="row mb-3">
								<label for="input-message" class="form-label">{{ entry_message }}</label>
								<textarea name="message" rows="3" id="input-message" class="form-control"></textarea>
								<div id="error-message" class="invalid-feedback"></div>
							</div>
							{{ captcha }}
						</fieldset>
						<div class="text-end">
							<button type="submit" class="btn btn-primary">{{ button_submit }}</button>
						</div>
					</form>
				</div>
			</div>
			{{ content_bottom }}</div>
		{{ column_right }}</div>
</div>
{{ footer }}
