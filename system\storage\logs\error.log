2025-06-10 14:29:09 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-10 14:29:10 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-10 14:29:10 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-10 14:29:16 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-10 14:29:16 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-10 14:30:00 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-10 14:30:01 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-12 19:24:03 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-12 19:24:05 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-12 19:24:05 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-12 19:24:10 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-12 19:24:11 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-12 19:24:14 - Error: Opencart\Admin\Model\Catalog\Product::getDiscounts(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 2250

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getDiscounts

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 360
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 112
Class: Opencart\Admin\Controller\Catalog\Product
Function: getList

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 202
Class: Opencart\System\Engine\Loader
Function: controller

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: list

Backtrace: 6
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 7
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-12 19:24:22 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-12 19:24:28 - Error: Opencart\Admin\Model\Catalog\Product::getDiscounts(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 2250

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getDiscounts

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 360
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 112
Class: Opencart\Admin\Controller\Catalog\Product
Function: getList

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 202
Class: Opencart\System\Engine\Loader
Function: controller

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: list

Backtrace: 6
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 7
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-12 19:24:30 - Error: Opencart\Admin\Model\Catalog\Product::getDiscounts(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 2250

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getDiscounts

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 360
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 112
Class: Opencart\Admin\Controller\Catalog\Product
Function: getList

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 153
Class: Opencart\System\Engine\Loader
Function: controller

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: index

Backtrace: 6
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 7
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-12 19:24:44 - Error: Opencart\Admin\Model\Catalog\Product::getDiscounts(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 2250

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getDiscounts

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 360
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 112
Class: Opencart\Admin\Controller\Catalog\Product
Function: getList

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 202
Class: Opencart\System\Engine\Loader
Function: controller

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: list

Backtrace: 6
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 7
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-13 22:49:11 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-13 22:49:14 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-13 22:49:14 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-13 22:49:15 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-13 22:49:16 - Error: Opencart\Admin\Model\Catalog\Product::getOptions(): Argument #1 ($product_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/product.php
Line: 1918

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Product
Function: getOptions

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1491
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-16 13:10:20 - Error: Opencart\Admin\Model\Catalog\Option::getValue(): Argument #1 ($option_value_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/option.php
Line: 409

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Option
Function: getValue

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1500
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-16 13:10:20 - Error: Opencart\Admin\Model\Catalog\Option::getValue(): Argument #1 ($option_value_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/option.php
Line: 409

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Option
Function: getValue

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1500
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-16 13:10:21 - Error: Opencart\Admin\Model\Catalog\Option::getValue(): Argument #1 ($option_value_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/option.php
Line: 409

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Option
Function: getValue

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1500
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-16 13:10:21 - Error: Opencart\Admin\Model\Catalog\Option::getValue(): Argument #1 ($option_value_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/option.php
Line: 409

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Option
Function: getValue

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1500
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-16 13:10:23 - Error: Opencart\Admin\Model\Catalog\Option::getValue(): Argument #1 ($option_value_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/option.php
Line: 409

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Option
Function: getValue

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1500
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-16 13:10:30 - Error: Opencart\Admin\Model\Catalog\Option::getValue(): Argument #1 ($option_value_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/option.php
Line: 409

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Option
Function: getValue

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1500
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-16 13:10:30 - Error: Opencart\Admin\Model\Catalog\Option::getValue(): Argument #1 ($option_value_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/option.php
Line: 409

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Option
Function: getValue

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1500
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-16 13:10:30 - Error: Opencart\Admin\Model\Catalog\Option::getValue(): Argument #1 ($option_value_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/option.php
Line: 409

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Option
Function: getValue

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1500
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-16 13:10:30 - Error: Opencart\Admin\Model\Catalog\Option::getValue(): Argument #1 ($option_value_id) must be of type int, null given, called in /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php on line 343
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/model/catalog/option.php
Line: 409

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/loader.php
Line: 343
Class: Opencart\Admin\Model\Catalog\Option
Function: getValue

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/proxy.php
Line: 87
Class: Opencart\System\Engine\Loader
Function: Opencart\System\Engine\{closure}

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/controller/catalog/product.php
Line: 1500
Class: Opencart\System\Engine\Proxy
Function: __call

Backtrace: 3
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/engine/action.php
Line: 96
Class: Opencart\Admin\Controller\Catalog\Product
Function: autocomplete

Backtrace: 4
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 259
Class: Opencart\System\Engine\Action
Function: execute

Backtrace: 5
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-19 18:12:38 - Error: Error: Could not make a database link using u111267912_pondexpo@localhost!<br/>Message: No such file or directory
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/library/db/mysqli.php
Line: 86

Backtrace: 0
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/library/db.php
Line: 40
Class: Opencart\System\Library\DB\MySQLi
Function: __construct

Backtrace: 1
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/system/framework.php
Line: 148
Class: Opencart\System\Library\DB
Function: __construct

Backtrace: 2
File: /home/<USER>/domains/webdevserver.in/public_html/pondexpo/admin/index.php
Line: 20
Function: require_once
2025-06-25 19:11:11 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 114
2025-06-25 19:11:15 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 114
2025-06-25 19:12:04 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 114
2025-06-25 19:17:35 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:18:16 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:29:25 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:29:41 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:30:00 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:33:12 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:33:13 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:33:15 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:33:18 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:33:22 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:33:28 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:33:29 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:34:09 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:34:10 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:34:12 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:34:15 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:34:20 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:34:24 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:45:30 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:45:32 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:46:04 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:46:46 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:46:48 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:55:43 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:55:44 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 19:56:23 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 115
2025-06-25 20:08:02 - PHP Warning:  Undefined array key "error" in D:\wamp64\www\pex\pondexpo\catalog\controller\checkout\payment_method.php on line 114
2025-07-14 09:01:11 - PHP Warning:  mail(): Failed to connect to mailserver at &quot;localhost&quot; port 25, verify your &quot;SMTP&quot; and &quot;smtp_port&quot; setting in php.ini or use ini_set() in D:\wamp64\www\pex\pondexpo\system\library\mail\mail.php on line 109
2025-07-14 09:01:13 - PHP Warning:  mail(): Failed to connect to mailserver at &quot;localhost&quot; port 25, verify your &quot;SMTP&quot; and &quot;smtp_port&quot; setting in php.ini or use ini_set() in D:\wamp64\www\pex\pondexpo\system\library\mail\mail.php on line 109
2025-07-14 09:01:35 - PHP Warning:  mail(): Failed to connect to mailserver at &quot;localhost&quot; port 25, verify your &quot;SMTP&quot; and &quot;smtp_port&quot; setting in php.ini or use ini_set() in D:\wamp64\www\pex\pondexpo\system\library\mail\mail.php on line 109
