<?php
// Heading
$_['heading_title']  = 'Make an Offer';
$_['form_title']  = 'Fill Form to Make an Offer';

// Text
$_['text_description'] = '<h3>Here’s How “Make An Offer” Works</h3>
<p>Most of the time, we are able to sell certain items on the site at prices lower than listed. Seasonal issues, inventory levels and current costs all contribute to the price at which an item can be sold. We are happy to be able to pass savings on to our valued customers when we can sell an item at a lower price.</p>
<p>Thus, we have created a Make An Offer process that can provide savings to you.</p>
<p>Keep in mind that when you purchase an item from PondExpo using the make an offer process, you are buying your item from us, exactly the same as if you had added the item to the shopping cart on the site, except you will be making your purchase at a lower price. In fact, once your offer is accepted, we will send you instructions on how to buy the item on our site at the agreed upon offer price. Also, all the benefits from buying at PondExpo will apply to your Make An Offer purchase. Free Shipping by UPS and FedEx, Exchanges, Returns, Security, even Free Pro Help with installation of your item. You Can Review Our Policies Here</p>
<p>Additionally, you need not provide a Credit Card until your offer is accepted, so you really have nothing to loose in making an offer.</p>
<hr>
<h3><strong><u>How do I Make an Offer?<br>
</u></strong></h3>
<p>Once you decide that you want to make an offer, simply fill in the Offer Form on this page and send it to us.</p>
<p>You will receive a confirmation of your offer as an email from us confirming your offer. Once your offer is received, we will be corresponding by email, and we will respond with acceptance or with a counter offer, usually the same day but no longer than 24 hours later. You can submit up to 3 offers for the same product.</p>
<p>Once your offer is accepted, we will send instructions on how to purchase the item on our site at the agreed upon price. You should plan to make your purchase as soon as the instructions are received. You will receive a receipt for your purchase, and then shipping and tracking information as soon as your purchased item is shipped, usually the next day.</p>
<p><a href="https://www.pondexpo.com/contact-us/">If you have any questions, use the form on our contact page or call 1-800-531-4535</a></p>
<p>Send Us Your Offer and Good Luck!</p>';
$_['text_message']   = '<p>Your offer has been successfully sent to the store owner!</p>';

// Entry
$_['entry_itemdescription']     = 'Describe the Product';
$_['entry_currentprice']        = 'Product Current Price';
$_['entry_offerprice']          = 'Your Offer Price';
$_['entry_ziptoship']           = 'Zip to Ship To';
$_['entry_name']                = 'Your Name';
$_['entry_email']               = 'E-Mail Address';
$_['entry_message']             = 'Additional Message';

// Button
$_['button_submit']             = 'Submit Offer';

// Email
$_['email_subject']  = 'New Offer from %s';
$_['email_message']  = "Product Description: %s\nCurrent Price: %s\nOffer Price: %s\nZip to Ship To: %s\nName: %s\nEmail: %s\n\nMessage:\n%s";

// Errors
$_['error_name']     = 'Name must be between 3 and 32 characters!';
$_['error_email']    = 'E-Mail Address does not appear to be valid!';
$_['error_product']  = 'Product description must be between 3 and 255 characters!';
$_['error_price']    = 'Please enter a valid price greater than zero!';
$_['error_ziptoship'] = 'Zip code must be between 5 and 10 characters!';
$_['error_message']  = 'Message must be between 10 and 3000 characters!';